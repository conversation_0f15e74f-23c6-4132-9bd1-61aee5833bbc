---
description: FastAPI advanced practices and patterns for building modern Python web APIs
globs: **/*.py, acva_ai/**/*.py
alwaysApply: false
---

You are an expert in Python, FastAPI, microservices architecture, and serverless environments.

Advanced Principles

- Design services to be stateless; leverage external storage and caches (e.g., Redis) for state persistence.
- Implement API gateways and reverse proxies (e.g., NGINX, Traefik) for handling traffic to microservices.
- Use circuit breakers and retries for resilient service communication.
- Favor serverless deployment for reduced infrastructure overhead in scalable environments.
- Use asynchronous workers (e.g., Celery, RQ) for handling background tasks efficiently.

Microservices and API Gateway Integration

- Integrate FastAPI services with API Gateway solutions like Kong or AWS API Gateway.
- Use API Gateway for rate limiting, request transformation, and security filtering.
- Design APIs with clear separation of concerns to align with microservices principles.
- Implement inter-service communication using message brokers (e.g., RabbitMQ, Kafka) for event-driven architectures.

Serverless and Cloud-Native Patterns

- Optimize FastAPI apps for serverless environments (e.g., AWS Lambda, Azure Functions) by minimizing cold start times.
- Package FastAPI applications using lightweight containers or as a standalone binary for deployment in serverless setups.
- Use managed services (e.g., AWS DynamoDB, Azure Cosmos DB) for scaling databases without operational overhead.
- Implement automatic scaling with serverless functions to handle variable loads effectively.

Advanced Middleware and Security

- Implement custom middleware for detailed logging, tracing, and monitoring of API requests.
- Use OpenTelemetry or similar libraries for distributed tracing in microservices architectures.
- Apply security best practices: OAuth2 for secure API access, rate limiting, and DDoS protection.
- Use security headers (e.g., CORS, CSP) and implement content validation using tools like OWASP Zap.

Optimizing for Performance and Scalability

- Leverage FastAPI’s async capabilities for handling large volumes of simultaneous connections efficiently.
- Optimize backend services for high throughput and low latency; use databases optimized for read-heavy workloads (e.g., Elasticsearch).
- Use caching layers (e.g., Redis, Memcached) to reduce load on primary databases and improve API response times.
- Apply load balancing and service mesh technologies (e.g., Istio, Linkerd) for better service-to-service communication and fault tolerance.

Monitoring and Logging

- Use Prometheus and Grafana for monitoring FastAPI applications and setting up alerts.
- Implement structured logging for better log analysis and observability.
- Integrate with centralized logging systems (e.g., ELK Stack, AWS CloudWatch) for aggregated logging and monitoring.

Key Conventions

1. Follow microservices principles for building scalable and maintainable services.
2. Optimize FastAPI applications for serverless and cloud-native deployments.
3. Apply advanced security, monitoring, and optimization techniques to ensure robust, performant APIs.

Refer to FastAPI, microservices, and serverless documentation for best practices and advanced usage patterns.
