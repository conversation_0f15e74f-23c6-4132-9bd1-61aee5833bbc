# ACVA-AI Test Scripts

This directory contains utility scripts for testing and interacting with the ACVA-AI system.

## Scripts

### `test_visit_job_flow.py`

A comprehensive test script that validates the complete visit job processing flow:

1. **Audio Splitting**: Takes a WAV file and splits it into 2-second chunks
2. **Visit Job Creation**: Creates a new visit job via API
3. **Batch Submission**: Submits audio chunks in batches of 25 files
4. **Progress Monitoring**: Monitors processing progress until completion
5. **Result Download**: Downloads the final processed audio file

#### Usage

```bash
python test_visit_job_flow.py <api_key> <wav_file_path> [options]
```

#### Examples

```bash
# Basic usage with local API
python test_visit_job_flow.py your-api-key recording.wav

# Custom API endpoint
python test_visit_job_flow.py your-api-key recording.wav --base-url http://production-api.com

# Custom chunk duration and batch size
python test_visit_job_flow.py your-api-key recording.wav --chunk-duration 1.5 --batch-size 20

# With webhook notifications
python test_visit_job_flow.py your-api-key recording.wav --webhook-url https://your-webhook.com/notify
```

#### Options

- `--base-url`: API base URL (default: `http://localhost:8000`)
- `--chunk-duration`: Duration of each audio chunk in seconds (default: `2.0`)
- `--batch-size`: Number of files per batch (default: `25`)
- `--webhook-url`: Optional webhook URL for progress notifications

#### Prerequisites

1. **Install dependencies**:

   ```bash
   pip install -r scripts/requirements.txt
   ```

2. **Environment setup**: Ensure you have a `.env` file in the project root with the necessary configuration for the download script (MongoDB, MinIO, etc.)

3. **API access**: Valid API key for the ACVA-AI system

4. **Audio file**: A WAV format audio file to test with

#### What it tests

- ✅ Audio file splitting into precise chunks
- ✅ Visit job creation via API
- ✅ Batch submission (handles large numbers of files)
- ✅ Progress monitoring and status checking
- ✅ Error handling and recovery
- ✅ Final result download integration

### `download_visit_audio.py`

Downloads the concatenated audio file for a completed visit job.

#### Usage

```bash
python download_visit_audio.py <visit_job_id>
```

#### Example

```bash
python download_visit_audio.py b9d27f50-d12e-449e-b615-7b9e42e91405
```

## Development Notes

### API Endpoints Used

The test script interacts with these API endpoints:

- `POST /api/v1/visit-jobs/create` - Create visit job
- `POST /api/v1/visit-jobs/{id}/batches` - Submit batch
- `POST /api/v1/visit-jobs/{id}/finish` - Signal completion
- `GET /api/v1/visit-jobs/{id}/status` - Check progress

### Error Handling

The script includes comprehensive error handling for:

- Invalid audio files
- API authentication failures
- Network connectivity issues
- Processing failures
- Download failures

### Logging

Detailed logging shows progress through each step:

```
2024-01-15 10:30:00 - INFO - 🎵 Starting Visit Job Flow Test
2024-01-15 10:30:00 - INFO - 📂 Step 1: Splitting audio into chunks...
2024-01-15 10:30:01 - INFO - Created 150 chunks
2024-01-15 10:30:01 - INFO - 📦 Step 2: Creating batches...
2024-01-15 10:30:01 - INFO - Created 6 batches (max 25 files per batch)
...
```

### Testing Different Scenarios

You can test various scenarios by adjusting the parameters:

- **Small files**: Use `--chunk-duration 0.5` for many small chunks
- **Large batches**: Use `--batch-size 50` (if API allows)
- **Long recordings**: Test with recordings of different lengths
- **Network issues**: Test with unreliable connections

## Dependencies

All scripts require:

- Python 3.8+
- Dependencies listed in `scripts/requirements.txt`
- Access to ACVA-AI API and infrastructure
