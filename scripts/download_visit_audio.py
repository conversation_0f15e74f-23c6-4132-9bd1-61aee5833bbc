#!/usr/bin/env python3
"""
Download Visit Audio Script

Downloads the concatenated audio file for a given visit job ID.
Uses configuration from .env file and saves to ~/Downloads.

Usage:
    python download_visit_audio.py <visit_job_id>

Example:
    python download_visit_audio.py b9d27f50-d12e-449e-b615-7b9e42e91405
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# Load environment variables
load_dotenv()


def download_visit_audio(visit_job_id: str):
    """Download the concatenated audio file for a visit job."""
    try:
        # Import after loading .env
        from acva_ai.database.mongo import MongoDB
        from acva_ai.services.minio_client import MinioClient

        # Initialize MongoDB connection
        mongo = MongoDB()
        mongo.initialize()

        # Get visit job data
        print(f"Looking up visit job: {visit_job_id}")
        visit_job_data = mongo.get_visit_job(visit_job_id)

        if not visit_job_data:
            print(f"❌ Visit job {visit_job_id} not found.")
            return False

        print(f"✅ Found visit job with status: {visit_job_data.get('status')}")

        # Initialize MinIO client
        minio = MinioClient()

        # Try to find the audio file - it should be named with timestamp
        # First, list all files to find the one with our job ID
        all_files = minio.list_files("")
        matching_files = [
            f for f in all_files if f and visit_job_id in f and f.endswith(".wav")
        ]

        if not matching_files:
            print(f"❌ No audio file found for visit job {visit_job_id}")
            print("Available files with this job ID:")
            job_files = [f for f in all_files if f and visit_job_id in f]
            for f in job_files:
                print(f"  - {f}")
            return False

        # Use the first matching file (there should only be one)
        audio_file_name = matching_files[0]
        print(f"📁 Found audio file: {audio_file_name}")

        # Download the file
        print("⬇️  Downloading audio file...")
        audio_data = minio.download_file(audio_file_name)

        # Save to Downloads folder
        downloads_dir = Path.home() / "Downloads"
        output_file = downloads_dir / audio_file_name

        with open(output_file, "wb") as f:
            f.write(audio_data)

        file_size_mb = len(audio_data) / (1024 * 1024)
        print(f"✅ Successfully downloaded {file_size_mb:.2f}MB to: {output_file}")
        return True

    except Exception as e:
        print(f"❌ Error downloading audio: {e}")
        return False


def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) != 2:
        print("Usage: python download_visit_audio.py <visit_job_id>")
        print("\nExample:")
        print("  python download_visit_audio.py b9d27f50-d12e-449e-b615-7b9e42e91405")
        sys.exit(1)

    visit_job_id = sys.argv[1]

    # Validate job ID format (should be a UUID)
    if len(visit_job_id) != 36 or visit_job_id.count("-") != 4:
        print(f"❌ Invalid job ID format: {visit_job_id}")
        print(
            "Job ID should be in UUID format (e.g., b9d27f50-d12e-449e-b615-7b9e42e91405)"
        )
        sys.exit(1)

    print(f"🎵 Downloading audio for visit job: {visit_job_id}")
    print("=" * 60)

    success = download_visit_audio(visit_job_id)

    if success:
        print("=" * 60)
        print("🎉 Download completed successfully!")
    else:
        print("=" * 60)
        print("💥 Download failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
