#!/usr/bin/env python3
"""
Test Visit Job Flow Script

This script tests the complete visit job flow:
1. Takes a WAV recording and splits it into 2-second chunks
2. Creates a visit job via API
3. Submits chunks in batches of 25 files
4. Signals completion
5. Monitors progress until completion
6. Downloads the final processed audio

Usage:
    python test_visit_job_flow.py <api_key> <wav_file_path> [--base-url BASE_URL]

Example:
    python test_visit_job_flow.py your-api-key recording.wav
    python test_visit_job_flow.py your-api-key recording.wav --base-url http://localhost:8000
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import argparse
import io
import logging
import os
import subprocess
import time
from typing import List, Optional

import requests
from dotenv import load_dotenv
from pydub import AudioSegment

from acva_ai.llm.llm_providers.llm_provider import LLMProvider
from acva_ai.llm.transcript_providers.transcript_provider import TranscriptProvider
from acva_ai.models.visit_job import VisitJobCreate, VisitJobResponse, VisitJobStatus

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class VisitJobAPIClient:
    """Client for interacting with the Visit Job API."""

    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.headers = {
            "X-API-Key": api_key,
        }

    def create_visit_job(self) -> VisitJobResponse:
        """Create a new visit job."""
        url = f"{self.base_url}/api/v1/visit-jobs/create"

        visit_job_create = VisitJobCreate(
            llm_provider=LLMProvider.OPENAI,
            transcript_provider=TranscriptProvider.OPENAI,
            diarize=False,
        )

        response = requests.post(
            url, json=visit_job_create.model_dump(), headers=self.headers
        )
        response.raise_for_status()
        return VisitJobResponse(**response.json())

    def submit_batch(
        self,
        visit_job_id: str,
        audio_files: List[tuple],
        batch_number: Optional[int] = None,
    ) -> dict:
        """Submit a batch of audio files."""
        url = f"{self.base_url}/api/v1/visit-jobs/{visit_job_id}/batches"

        files = []
        for i, (filename, audio_data) in enumerate(audio_files):
            files.append(("files", (filename, audio_data, "audio/wav")))

        params = {}
        if batch_number is not None:
            params["batch_number"] = batch_number

        response = requests.post(url, files=files, headers=self.headers, params=params)
        response.raise_for_status()
        return response.json()

    def finish_visit_job(self, visit_job_id: str) -> dict:
        """Signal that all batches have been submitted."""
        url = f"{self.base_url}/api/v1/visit-jobs/{visit_job_id}/finish"

        response = requests.post(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def get_status(self, visit_job_id: str) -> dict:
        """Get the status of a visit job."""
        url = f"{self.base_url}/api/v1/visit-jobs/{visit_job_id}/status"

        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        return response.json()

    def get_visit_report(self, task_id: str) -> Optional[dict]:
        """Get the visit report for a task (if available)."""
        try:
            url = f"{self.base_url}/api/v1/tasks/task/{task_id}/visit-report"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                return None  # Report not yet available
            raise


def split_audio_into_chunks(
    audio_file_path: str, chunk_duration_seconds: float = 2.0
) -> List[tuple]:
    """
    Split audio file into chunks of specified duration.

    Returns:
        List of tuples (filename, audio_data_bytes)
    """
    logger.info(f"Loading audio file: {audio_file_path}")

    # Load the audio file
    audio = AudioSegment.from_file(audio_file_path)

    # Calculate chunk length in milliseconds
    chunk_length_ms = int(chunk_duration_seconds * 1000)

    # Get original filename without extension
    original_name = Path(audio_file_path).stem

    chunks = []
    total_duration_ms = len(audio)

    logger.info(f"Original audio duration: {total_duration_ms / 1000:.2f} seconds")
    logger.info(f"Splitting into {chunk_duration_seconds}-second chunks...")

    for i in range(0, total_duration_ms, chunk_length_ms):
        chunk = audio[i : i + chunk_length_ms]

        # Create filename for this chunk
        chunk_filename = f"{original_name}_chunk_{i // chunk_length_ms + 1:04d}.wav"

        # Export chunk to bytes
        chunk_io = io.BytesIO()
        chunk.export(chunk_io, format="wav")
        chunk_data = chunk_io.getvalue()

        chunks.append((chunk_filename, chunk_data))

        chunk_duration = len(chunk) / 1000
        logger.debug(
            f"Created chunk {len(chunks)}: {chunk_filename} ({chunk_duration:.2f}s)"
        )

    logger.info(f"Created {len(chunks)} chunks")
    return chunks


def create_batches(chunks: List[tuple], batch_size: int = 25) -> List[List[tuple]]:
    """Group chunks into batches of specified size."""
    batches = []
    for i in range(0, len(chunks), batch_size):
        batch = chunks[i : i + batch_size]
        batches.append(batch)

    logger.info(f"Created {len(batches)} batches (max {batch_size} files per batch)")
    for i, batch in enumerate(batches):
        logger.info(f"Batch {i + 1}: {len(batch)} files")

    return batches


def monitor_progress(
    client: VisitJobAPIClient, visit_job_id: str, check_interval: int = 3
) -> bool:
    """
    Monitor visit job progress until completion, with clear differentiation between
    batch processing and report generation phases.

    Returns:
        True if completed successfully, False if failed
    """
    logger.info(f"Monitoring progress for visit job: {visit_job_id}")

    # Track phases and milestones
    last_status = None
    batch_processing_complete = False
    assembly_complete = False
    report_generation_started = False

    # Track timing for phase durations
    start_time = time.time()
    batch_complete_time = None
    assembly_complete_time = None
    report_start_time = None

    phases = {
        "created": "⏳ Visit job created...",
        "receiving_batches": "⏳ Receiving batches...",
        "waiting_for_finish": "⏳ Waiting for finish signal...",
        "assembling_batches": "🔧 Assembling audio from all batches...",
        "report_generation": "📝 Generating medical report...",
        "completed": "✅ Visit job completed successfully!",
        "failed": "❌ Visit job failed!",
        "cancelled": "❌ Visit job cancelled!",
    }

    while True:
        try:
            status = client.get_status(visit_job_id)

            job_status = status.get("status")
            progress_data = status.get("progress", {})
            progress = progress_data.get("progress_percentage", 0)
            batches_processed = progress_data.get("batches_processed", 0)
            batches_received = progress_data.get("total_batches", 0)
            batches_failed = progress_data.get("batches_failed", 0)

            # Get additional timing information
            timestamps = status.get("timestamps", {})
            finished_at = timestamps.get("finished_at")
            completed_at = timestamps.get("completed_at")
            assembled_audio_path = status.get("assembled_audio_path")

            # Track major milestone transitions
            current_batch_complete = (
                batches_received > 0
                and batches_processed == batches_received
                and batches_failed == 0
                and finished_at is not None
            )

            current_assembly_complete = assembled_audio_path is not None
            current_report_started = (
                job_status == VisitJobStatus.REPORT_GENERATION.value
            )

            # Show milestone achievements with timing
            if current_batch_complete and not batch_processing_complete:
                batch_processing_complete = True
                batch_complete_time = time.time()
                batch_duration = batch_complete_time - start_time
                logger.info("🎯 MILESTONE: All batch processing completed!")
                logger.info(
                    f"   → Processed {batches_processed}/{batches_received} batches successfully"
                )
                logger.info(f"   → Batch processing took: {batch_duration:.1f} seconds")
                if finished_at:
                    logger.info(f"   → Batch processing finished at: {finished_at}")

            if current_assembly_complete and not assembly_complete:
                assembly_complete = True
                assembly_complete_time = time.time()
                if batch_complete_time:
                    assembly_duration = assembly_complete_time - batch_complete_time
                    logger.info("🎯 MILESTONE: Audio assembly completed!")
                    logger.info(f"   → Assembly took: {assembly_duration:.1f} seconds")
                else:
                    logger.info("🎯 MILESTONE: Audio assembly completed!")
                logger.info(
                    f"   → Assembled audio available at: {assembled_audio_path}"
                )

            if current_report_started and not report_generation_started:
                report_generation_started = True
                report_start_time = time.time()
                if assembly_complete_time:
                    wait_duration = report_start_time - assembly_complete_time
                    logger.info("🎯 MILESTONE: Report generation started!")
                    logger.info(
                        f"   → Waited {wait_duration:.1f} seconds after assembly completion"
                    )
                else:
                    logger.info("🎯 MILESTONE: Report generation started!")
                logger.info("   → Now generating comprehensive medical report...")

            # Show phase change messages
            if job_status and job_status != last_status and job_status in phases:
                logger.info(phases[job_status])
                last_status = job_status

            # Calculate overall workflow progress
            workflow_progress = progress  # Default to batch progress
            if job_status == VisitJobStatus.WAITING_FOR_FINISH.value:
                # Batch processing phase: 0-70% based on batch completion
                workflow_progress = (
                    (batches_processed / batches_received * 70)
                    if batches_received > 0
                    else 0
                )
            elif job_status == VisitJobStatus.ASSEMBLING_BATCHES.value:
                # Assembly phase: 70-80%
                workflow_progress = 75
            elif job_status == VisitJobStatus.REPORT_GENERATION.value:
                # Report generation phase: 80-95%
                workflow_progress = 85
            elif job_status == VisitJobStatus.COMPLETED.value:
                # Completed: 100%
                workflow_progress = 100

            # Show detailed status with context
            status_context = ""
            if job_status == VisitJobStatus.WAITING_FOR_FINISH.value:
                if batches_processed < batches_received:
                    status_context = (
                        f" (Processing batches: {batches_processed}/{batches_received})"
                    )
                else:
                    status_context = " (Waiting for final batch completion)"
            elif job_status == VisitJobStatus.ASSEMBLING_BATCHES.value:
                status_context = " (Concatenating all processed audio chunks)"
            elif job_status == VisitJobStatus.REPORT_GENERATION.value:
                status_context = (
                    " (Analyzing transcript and generating medical insights)"
                )

            # Build progress display
            progress_display = f"Overall Progress: {workflow_progress:.0f}%"

            # Add batch-specific progress during batch processing phase
            if (
                job_status == VisitJobStatus.WAITING_FOR_FINISH.value
                and batches_received > 0
            ):
                batch_progress = batches_processed / batches_received * 100
                progress_display += f" (Batches: {batch_progress:.0f}%)"

            logger.info(
                f"Status: {job_status}{status_context} | {progress_display} | "
                f"Batches: {batches_processed}/{batches_received} processed"
                + (f", {batches_failed} failed" if batches_failed > 0 else "")
            )

            # Check for completion
            if job_status == VisitJobStatus.COMPLETED.value:
                completion_time = time.time()
                total_duration = completion_time - start_time

                logger.info("🎯 MILESTONE: Complete workflow finished!")
                logger.info(f"   → Total processing time: {total_duration:.1f} seconds")

                # Show phase durations
                if report_start_time:
                    report_duration = completion_time - report_start_time
                    logger.info(
                        f"   → Report generation took: {report_duration:.1f} seconds"
                    )

                # Show final results
                if visit_job_id:
                    logger.info(f"   → Visit report generated: {visit_job_id}")
                if assembled_audio_path:
                    logger.info(f"   → Final audio available: {assembled_audio_path}")
                if completed_at:
                    logger.info(f"   → Completed at: {completed_at}")

                logger.info(
                    "🎉 Visit job and report generation completed successfully!"
                )
                return True

            elif job_status == VisitJobStatus.FAILED.value:
                logger.error("❌ Visit job failed!")
                error_msg = status.get("error_message")
                if error_msg:
                    logger.error(f"   → Error: {error_msg}")
                return False

            elif job_status == VisitJobStatus.CANCELLED.value:
                status_text = job_status if job_status else "unknown"
                logger.error(f"❌ Visit job {status_text}!")
                return False

            # Continue monitoring
            time.sleep(check_interval)

        except requests.exceptions.RequestException as e:
            logger.error(f"Error checking status: {e}")
            time.sleep(check_interval)
        except KeyboardInterrupt:
            logger.info("Monitoring interrupted by user")
            return False


def download_result(visit_job_id: str) -> bool:
    """Download the final processed audio using the existing download script."""
    logger.info("Downloading final processed audio...")

    try:
        # Get the path to the download script
        script_dir = Path(__file__).parent
        download_script = script_dir / "download_visit_audio.py"

        if not download_script.exists():
            logger.error(f"Download script not found: {download_script}")
            return False

        # Run the download script directly
        result = subprocess.run(
            [sys.executable, str(download_script), visit_job_id],
            capture_output=True,
            text=True,
        )

        logger.debug(f"Download script return code: {result.returncode}")
        if result.stdout:
            logger.debug(f"Download script stdout: {result.stdout}")
        if result.stderr:
            logger.debug(f"Download script stderr: {result.stderr}")

        if result.returncode == 0:
            logger.info("✅ Audio download completed successfully!")
            if result.stdout:
                logger.info(result.stdout)
            return True
        else:
            logger.error(
                f"❌ Audio download failed with return code {result.returncode}"
            )
            if result.stderr:
                logger.error(f"Error output: {result.stderr}")
            if result.stdout:
                logger.error(f"Standard output: {result.stdout}")
            return False

    except Exception as e:
        logger.error(f"Error downloading audio: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Test the complete visit job flow with audio splitting and batch processing"
    )
    parser.add_argument("api_key", help="API key for authentication")
    parser.add_argument("wav_file", help="Path to the WAV file to process")
    parser.add_argument(
        "--base-url",
        default="http://localhost:8000",
        help="Base URL of the API (default: http://localhost:8000)",
    )
    parser.add_argument(
        "--chunk-duration",
        type=float,
        default=2.0,
        help="Duration of each audio chunk in seconds (default: 2.0)",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=25,
        help="Number of files per batch (default: 25)",
    )

    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging for more detailed output",
    )

    args = parser.parse_args()

    # Set log level based on debug flag
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Validate inputs
    if not os.path.exists(args.wav_file):
        logger.error(f"Audio file not found: {args.wav_file}")
        sys.exit(1)

    if not args.wav_file.lower().endswith(".wav"):
        logger.error("Input file must be a WAV file")
        sys.exit(1)

    logger.info("🎵 Starting Visit Job Flow Test")
    logger.info("=" * 60)
    logger.info(f"API Base URL: {args.base_url}")
    logger.info(f"Audio File: {args.wav_file}")
    logger.info(f"Chunk Duration: {args.chunk_duration}s")
    logger.info(f"Batch Size: {args.batch_size} files")
    logger.info("=" * 60)

    try:
        # Initialize API client
        client = VisitJobAPIClient(args.base_url, args.api_key)

        # Step 1: Split audio into chunks
        logger.info("📂 Step 1: Splitting audio into chunks...")
        chunks = split_audio_into_chunks(args.wav_file, args.chunk_duration)

        # Step 2: Create batches
        logger.info("📦 Step 2: Creating batches...")
        batches = create_batches(chunks, args.batch_size)

        # Step 3: Create visit job
        logger.info("🆕 Step 3: Creating visit job...")
        visit_job_response = client.create_visit_job()

        visit_job_id = str(visit_job_response.task_id)
        logger.info(f"✅ Created visit job: {visit_job_id}")

        # Step 4: Submit batches
        logger.info("📤 Step 4: Submitting batches...")
        for i, batch in enumerate(batches):
            batch_number = i + 1
            logger.info(
                f"Submitting batch {batch_number}/{len(batches)} ({len(batch)} files)..."
            )

            batch_response = client.submit_batch(visit_job_id, batch, batch_number)
            logger.info(
                f"✅ Submitted batch {batch_number}: {batch_response['batch_id']}"
            )

        # Step 5: Signal completion
        logger.info("🏁 Step 5: Signaling completion...")
        finish_response = client.finish_visit_job(visit_job_id)
        logger.info(f"✅ Finish signal sent: {finish_response['message']}")

        # Step 6: Monitor progress
        logger.info("⏳ Step 6: Monitoring complete workflow...")
        logger.info("   This will track:")
        logger.info("   • Batch processing completion")
        logger.info("   • Audio assembly")
        logger.info("   • Report generation")
        logger.info("   • Final completion")
        success = monitor_progress(client, visit_job_id)

        if not success:
            logger.error("❌ Visit job did not complete successfully")
            sys.exit(1)

        # Step 7: Download result
        logger.info("⬇️  Step 7: Downloading result...")
        download_success = download_result(visit_job_id)

        if download_success:
            logger.info("=" * 60)
            logger.info("🎉 Visit Job Flow Test Completed Successfully!")
            logger.info(f"Visit Job ID: {visit_job_id}")

            # Try to get visit report information
            try:
                # The visit_job_id should be available in the final status
                final_status = client.get_status(visit_job_id)
                visit_job_id = final_status.get("task_id")
                if visit_job_id:
                    logger.info(f"📋 Visit Report ID: {visit_job_id}")

                    # Try to fetch the actual report
                    report = client.get_visit_report(visit_job_id)
                    if report:
                        logger.info(
                            "📄 Visit report is available and can be accessed via API"
                        )
                    else:
                        logger.info(
                            "📄 Visit report ID found but report may still be processing"
                        )
                else:
                    logger.info("📄 No visit report ID found in final status")
            except Exception as e:
                logger.warning(f"Could not retrieve visit report information: {e}")

            logger.info("=" * 60)
        else:
            logger.warning("⚠️  Visit job completed but download failed")
            logger.info(
                f"You can manually download using: python download_visit_audio.py {visit_job_id}"
            )

    except requests.exceptions.RequestException as e:
        logger.error(f"❌ API request failed: {e}")
        if hasattr(e, "response") and e.response is not None:
            logger.error(f"Response: {e.response.text}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
