# ACVA-AI: Medical Consultation Processing System

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.68+-green.svg)](https://fastapi.tiangolo.com/)
[![Docker](https://img.shields.io/badge/docker-%230db7ed.svg?style=flat&logo=docker&logoColor=white)](https://www.docker.com/)

**ACVA-AI** is a comprehensive AI-powered system that transforms medical consultation audio recordings into structured, enhanced medical reports. It combines advanced speech recognition, natural language processing, and medical knowledge retrieval to create accurate, detailed medical documentation.

## 🎯 Overview

This system acts as an **AI Medical Scribe** that:

- **Transcribes** audio consultations with high accuracy using AI services
- **Extracts** medications, symptoms, and medical conditions from transcripts
- **Enhances** content with medical knowledge from databases and web searches
- **Generates** structured medical reports with domain-specific insights
- **Integrates** seamlessly with existing medical workflows

## ✨ Key Features

### 🎵 Audio Processing

- **Dual Transcription Engines**: OpenAI Whisper and Vatis API support
- **Word-level Accuracy**: Timestamps and confidence scores for each word
- **Quality Assurance**: Automatic correction of low-confidence segments
- **Multi-format Support**: WAV audio file processing

### 🧠 AI-Powered Enhancement

- **Medical Entity Extraction**: Automatic identification of medications and conditions
- **Context-Aware Corrections**: Uses surrounding text for better accuracy
- **Knowledge Augmentation**: RAG system with medical terminology database
- **Multi-language Support**: Primary Romanian with English fallbacks

### 🔍 Intelligent Search

- **Medication Information**: Automatic web search for drug prospects and references
- **Medical Knowledge Base**: Vector database search for symptoms and conditions
- **Real-time Enhancement**: Live integration with medical information sources

### 📊 Structured Reporting

- **Configurable Reports**: Customizable medical report sections
- **Domain-specific Insights**: Specialized analysis for different medical areas
- **Professional Output**: Structured, clinical-grade documentation

### 🏗️ Production Ready

- **Containerized Deployment**: Docker-based with health checks
- **Scalable Architecture**: Async processing with resource monitoring
- **API Security**: Authentication and rate limiting
- **Error Recovery**: Robust error handling and retry mechanisms

## 🏛️ Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Audio Input   │───▶│  Transcription  │───▶│ Text Processing │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Medical Report │◀───│  Web Search &   │◀───│ Medical Entity  │
│   Generation    │    │   RAG System    │    │   Extraction    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

- **Backend**: Python, FastAPI, Gunicorn + Uvicorn
- **Databases**: MongoDB, PostgreSQL, Qdrant (Vector DB)
- **AI Services**: OpenAI GPT models, Vatis API
- **External APIs**: Brave Search, Firecrawl
- **Infrastructure**: Docker, Docker Compose

### Processing Pipeline

1. **Audio Transcription** → Convert audio to text with confidence scores
2. **Text Chunking** → Split transcript into manageable segments
3. **Quality Enhancement** → Correct low-confidence segments using AI
4. **Medical Extraction** → Identify medications, symptoms, and conditions
5. **Knowledge Augmentation** → Search medical databases and web sources
6. **Report Generation** → Create structured medical documentation
7. **Callback Integration** → Send results to external systems

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.8+ (for local development)
- API keys for required services

### Required API Keys

```bash
# Core Services
OPENAI_API=your_openai_api_key
API_KEY_VETIS=your_vatis_api_key

# Search Services
BRAVE_API=your_brave_search_api_key
FIRECRAWL_API=your_firecrawl_api_key

# Internal Authentication
API_KEY=your_internal_api_key

# External Integration
ACVA_CALLBACK_URL=your_callback_endpoint
ACVA_REPORT_FIELDS=your_report_fields_endpoint
```

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd acva-ai
   ```

2. **Set up environment variables**

   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Configure directories**

   ```bash
   # Create required directories
   export TMP_DATA_DIR=/path/to/tmp/data
   export QDRANT_FOLDER=/path/to/qdrant/storage

   mkdir -p $TMP_DATA_DIR
   mkdir -p $QDRANT_FOLDER
   ```

4. **Start the services**

   ```bash
   docker-compose up -d
   ```

5. **Verify installation**
   ```bash
   curl http://localhost:8000/docs
   ```

## ⚙️ Configuration

### Environment Variables

| Variable                                              | Description                         | Required |
| ----------------------------------------------------- | ----------------------------------- | -------- |
| `OPENAI_API`                                          | OpenAI API key for GPT models       | Yes      |
| `API_KEY_VETIS`                                       | Vatis transcription service API key | Yes      |
| `BRAVE_API`                                           | Brave Search API key                | Yes      |
| `FIRECRAWL_API`                                       | Firecrawl web scraping API key      | Yes      |
| `MONGODB`                                             | MongoDB connection string           | Yes      |
| `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, `DB_PASS` | PostgreSQL configuration            | Yes      |
| `QDRANT_SERVER`                                       | Qdrant vector database server       | Yes      |
| `TMP_DATA_DIR`                                        | Temporary file storage directory    | Yes      |
| `ACVA_CALLBACK_URL`                                   | External callback endpoint          | Yes      |
| `ACVA_REPORT_FIELDS`                                  | Report configuration endpoint       | Yes      |
| `API_KEY`                                             | Internal API authentication key     | Yes      |

### Docker Configuration

```yaml
# docker-compose.yml structure
services:
  backend: # Main application server
  qdrant_db: # Vector database for medical knowledge
```

### Database Setup

The system automatically creates required database schemas on startup. Ensure your databases are accessible with the provided credentials.

## 📚 API Documentation

### Core Endpoints

#### Audio Processing

```http
POST /audio/process
Content-Type: multipart/form-data

# Upload audio file for processing
# Returns task_id for status tracking
```

#### Task Management

```http
GET /task/{task_id}/status
# Check processing status

GET /task/{task_id}/result
# Retrieve processing results
```

#### Medical Reports

```http
GET /medical-note/{task_id}
# Get enhanced medical report

POST /medical-note/enhance
# Trigger report enhancement
```

### Authentication

All API endpoints require authentication via API key:

```http
X-API-Key: your_api_key
```

### Response Format

```json
{
  "task_id": "uuid",
  "status": "audio_processing|done|error",
  "transcript": "processed_transcript",
  "simptome": ["list_of_symptoms"],
  "medicamente": {
    "medicament": [
      {
        "denumire": "medication_name",
        "prospect": "drug_information",
        "referinta": "reference_url"
      }
    ]
  },
  "observatii": ["processing_observations"],
  "medical_report": {
    "section1": "enhanced_content",
    "section2": "enhanced_content"
  }
}
```

## 🔧 Usage Examples

### Basic Audio Processing

```python
import requests

# Upload audio file
files = {'file': open('consultation.wav', 'rb')}
headers = {'X-API-Key': 'your_api_key'}

response = requests.post(
    'http://localhost:8000/audio/process',
    files=files,
    headers=headers
)

task_id = response.json()['task_id']

# Check status
status_response = requests.get(
    f'http://localhost:8000/task/{task_id}/status',
    headers=headers
)

# Get results when complete
if status_response.json()['status'] == 'done':
    results = requests.get(
        f'http://localhost:8000/task/{task_id}/result',
        headers=headers
    )
```

### Advanced Configuration

```python
# Custom prompt management
prompts_response = requests.get(
    'http://localhost:8000/prompts/',
    headers=headers
)

# Cost monitoring
costs = requests.get(
    f'http://localhost:8000/costs/{task_id}',
    headers=headers
)
```

## 🏥 Medical Knowledge Integration

### RAG System

- **Vector Database**: Qdrant stores medical terminology embeddings
- **Semantic Search**: Finds relevant conditions from symptom descriptions
- **Knowledge Base**: Comprehensive medical dictionary integration

### Web Search Enhancement

- **Medication Research**: Automatic lookup of drug information
- **Romanian Medical Sources**: Specialized search for local medical content
- **Reference Validation**: Cross-reference multiple medical sources

### Domain-Specific Processing

- **Configurable Sections**: Customize report sections via external API
- **Specialty Support**: Adaptable to different medical specialties
- **Quality Assurance**: Multiple validation layers for medical accuracy

## 🔍 Monitoring and Debugging

### Logging

```bash
# View application logs
docker-compose logs -f backend

# Database logs
docker-compose logs -f qdrant_db
```

### Cost Tracking

The system automatically tracks:

- LLM API usage costs
- Processing time per stage
- Resource utilization metrics

### Health Checks

```bash
# Service health
curl http://localhost:8000/health

# Database connectivity
curl http://localhost:6333/collections
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Local development
pip install -r resources/requirements.txt
python -m acva_ai.api.main

# Run tests
python -m pytest tests/

# Code formatting
black acva_ai/
isort acva_ai/
```

### Utility Scripts

#### Download Visit Audio

To download the concatenated audio file for a specific visit job, use the `download_visit_audio.py` script. This script automatically uses your MinIO and MongoDB configurations from the `.env` file.

```bash
# Ensure your Python virtual environment is activated
source .venv/bin/activate

# Run the script with the visit job ID
python -m scripts.download_visit_audio <visit_job_id>
```

## 📋 System Requirements

### Minimum Requirements

- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB SSD
- **Network**: Stable internet for API calls

### Recommended for Production

- **CPU**: 8+ cores
- **RAM**: 16GB+
- **Storage**: 200GB+ SSD
- **Network**: High-bandwidth connection
- **Load Balancer**: For multiple instances

## 🐛 Troubleshooting

### Common Issues

**Audio Processing Fails**

```bash
# Check API keys
echo $API_KEY_VETIS
echo $OPENAI_API

# Verify file format
file consultation.wav
```

**Database Connection Issues**

```bash
# Check MongoDB
docker-compose logs qdrant_db

# Verify PostgreSQL
docker-compose exec backend pg_isready
```

**High Processing Costs**

```bash
# Monitor usage
curl http://localhost:8000/costs/summary

# Check caching
ls -la /path/to/cache/openai/
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- **Issues**: Create a GitHub issue
- **Documentation**: Check the `/docs` endpoint
- **API Reference**: Visit `/docs` for interactive API documentation

## 🗺️ Roadmap

- [ ] **Multi-language Support**: Expand beyond Romanian
- [ ] **Real-time Processing**: Live audio transcription
- [ ] **Advanced Analytics**: Medical trend analysis
- [ ] **Mobile Integration**: Mobile app support
- [ ] **FHIR Compliance**: Healthcare standard integration
- [ ] **Voice Biometrics**: Speaker identification
- [ ] **Clinical Decision Support**: AI-powered recommendations

---

**Built with ❤️ for the medical community**
