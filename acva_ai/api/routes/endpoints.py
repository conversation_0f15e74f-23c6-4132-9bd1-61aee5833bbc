import logging

from fastapi import APIRouter

from acva_ai.api.routes.methods_routes import methods_router
from acva_ai.api.routes.pipeline_routes import pipeline_router
from acva_ai.api.routes.task_routes import task_router
from acva_ai.api.routes.visit_job_routes import visit_job_router

logger = logging.getLogger(__name__)

api_router = APIRouter()


api_router.include_router(methods_router, prefix="", tags=["Methods"])
# api_router.include_router(prompt_router, prefix="/prompts", tags=["Prompts"])

# Include the comprehensive pipeline router
api_router.include_router(pipeline_router, prefix="/pipeline", tags=["Pipeline"])


api_router.include_router(
    visit_job_router, prefix="/visit-jobs", tags=["Visit Job Processing"]
)

# Include existing routers
api_router.include_router(task_router, prefix="/tasks", tags=["Tasks"])
