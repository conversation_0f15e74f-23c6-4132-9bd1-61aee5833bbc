import asyncio
import json
import logging
from datetime import datetime

from fastapi import APIRout<PERSON>, Depends, HTTPException
from fastapi.responses import JSONResponse, StreamingResponse

from acva_ai.database import mongo_instance
from acva_ai.utils.security_service import SecurityService

logger = logging.getLogger(__name__)

task_router = APIRouter(dependencies=[Depends(SecurityService.get_api_key)])


def serialize_datetime(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    if isinstance(obj, dict):
        return {k: serialize_datetime(v) for k, v in obj.items()}
    if isinstance(obj, list):
        return [serialize_datetime(i) for i in obj]
    return obj


@task_router.get("/get-tasks")
async def get_tasks():
    """
    Retrieves a list of all tasks from the database.

    Each task object typically contains fields like `task_id`, `status`, etc.
    The exact structure depends on the `Task` model and database content.
    """
    tasks = mongo_instance.get_tasks()
    return JSONResponse(status_code=200, content=tasks)


@task_router.get("/task/{task_id}/stream-status")
async def stream_task_status(task_id: str):
    """
    Streams the status of a task as server-sent events.
    """
    task = mongo_instance.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    async def status_event_stream():
        last_status = None

        while True:
            task_status = mongo_instance.get_task(task_id)
            if not task_status:
                yield f"data: {json.dumps({'error': 'Task not found'})}\n\n"
                break

            current_status = task_status["status"]

            if current_status != last_status:
                last_status = current_status

                if current_status == "task_completed":
                    result_task = mongo_instance.get_medical_report(task_id=task_id)
                    yield f"data: {json.dumps({'status': 'completed', 'result': result_task})}\n\n"
                    break
                else:
                    yield f"data: {json.dumps({'status': current_status})}\n\n"

            await asyncio.sleep(1)  # avoid tight loop

    return StreamingResponse(status_event_stream(), media_type="text/event-stream")


@task_router.get("/task/{task_id}/visit-report")
async def get_visit_report(task_id: str):
    """
    Retrieves the visit report for a specific task by its ID.

    The visit report typically contains fields like `task_id`, `medical_report`, etc.
    The exact structure depends on the `VisitReport` model and database content.
    """
    visit_report = mongo_instance.get_visit_report(task_id)
    if not visit_report:
        raise HTTPException(status_code=404, detail="Visit report not found")

    return visit_report


@task_router.get("/task/{task_id}/status")
async def get_task_status(task_id: str):
    """
    Fetches the current status of a specific task by its ID.

    - If the task status is 'task_completed', it returns the final audio_processing result
      (e.g., the medical report) fetched from `mongo_instance.get_medical_report`.
    - Otherwise, it returns the `task_id` and the current `status` (e.g., 'audio_processing', 'error').

    Raises 404 if the `task_id` is not found.
    """
    task = mongo_instance.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    status = mongo_instance.get_processing_status(task_id=task_id)
    return JSONResponse(
        status_code=200,
        content={
            "status": serialize_datetime(status.dict()) if status else None,
        },
    )
