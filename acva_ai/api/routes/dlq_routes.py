import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

from acva_ai.models.dlq_job import DLQJobInfo, DLQJobResponse, DLQStatsResponse
from acva_ai.models.job_metadata import JobMetadata
from acva_ai.services.queue_config import queue_client
from acva_ai.utils.security_service import SecurityService

logger = logging.getLogger(__name__)

dlq_router = APIRouter(dependencies=[Depends(SecurityService.get_api_key)])


def _is_retries_exhausted(metadata: Optional[JobMetadata]) -> bool:
    """Assume metadata is a JobMetadata object (or None). Return True when retries_left == 0."""
    if not metadata:
        return False
    try:
        retry_info = metadata.execution_metadata.retry_info
        if not retry_info:
            return False
        rl = retry_info.retries_left
        return rl is not None and int(rl) == 0
    except Exception:
        return False


@dlq_router.get("/jobs", response_model=List[DLQJobResponse])
async def get_dlq_jobs(
    limit: int = Query(50, ge=1, le=200, description="Number of jobs to return"),
    offset: int = Query(0, ge=0, description="Number of jobs to skip"),
):
    """
    Get failed jobs from the Dead Letter Queue with pagination.

    Returns detailed information about failed jobs including error context,
    timing information, and requeue capability assessment.
    """
    try:
        jobs = queue_client.get_dlq_jobs(limit=limit, offset=offset)

        responses = []

        for job_info in jobs:
            # Assess requeue capability
            can_requeue = True
            requeue_reason = None

            if not job_info.function_name:
                can_requeue = False
                requeue_reason = "Missing function name"
            elif _is_retries_exhausted(job_info.metadata):
                can_requeue = False
                requeue_reason = "Maximum retries exceeded"

            responses.append(
                DLQJobResponse(
                    job_info=job_info,
                    can_requeue=can_requeue,
                    requeue_reason=requeue_reason,
                )
            )

        return responses

    except Exception as e:
        logger.error(f"Error getting DLQ jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve DLQ jobs")


@dlq_router.get("/jobs/{job_id}", response_model=DLQJobResponse)
async def get_dlq_job(job_id: str):
    """
    Get detailed information about a specific failed job in the DLQ.
    """
    try:
        job_info = queue_client.get_dlq_job_details(job_id)

        if not job_info:
            raise HTTPException(status_code=404, detail="DLQ job not found")

        # Assess requeue capability
        can_requeue = True
        requeue_reason = None

        if not job_info.function_name:
            can_requeue = False
            requeue_reason = "Missing function name"
        elif _is_retries_exhausted(job_info.metadata):
            can_requeue = False
            requeue_reason = "Maximum retries exceeded"

        return DLQJobResponse(
            job_info=job_info, can_requeue=can_requeue, requeue_reason=requeue_reason
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting DLQ job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve DLQ job")


@dlq_router.post("/jobs/{job_id}/requeue")
async def requeue_dlq_job(job_id: str):
    """
    Requeue a failed job from the DLQ back to its original queue or a specified queue.

    The job will be removed from the DLQ and added back to the processing queue.
    """
    try:
        # Get job details first to validate
        job_info = queue_client.get_dlq_job_details(job_id)
        if not job_info:
            raise HTTPException(status_code=404, detail="DLQ job not found")

        # Check if job can be requeued
        if not job_info.function_name:
            raise HTTPException(
                status_code=400, detail="Cannot requeue job: missing function name"
            )

        if _is_retries_exhausted(job_info.metadata):
            raise HTTPException(
                status_code=400, detail="Cannot requeue job: maximum retries exceeded"
            )

        success = queue_client.requeue_dlq_job(job_id)

        if success:
            return JSONResponse(
                {
                    "message": f"Successfully requeued job {job_id}",
                    "job_id": job_id,
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to requeue job")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error requeuing DLQ job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to requeue job")


@dlq_router.delete("/jobs/{job_id}")
async def clear_dlq_job(job_id: str):
    """
    Remove a job from the DLQ without requeuing it.

    This permanently removes the job from tracking.
    """
    try:
        # Check if job exists
        job_info = queue_client.get_dlq_job_details(job_id)
        if not job_info:
            raise HTTPException(status_code=404, detail="DLQ job not found")

        success = queue_client.clear_dlq_job(job_id)

        if success:
            return JSONResponse(
                {
                    "message": f"Successfully cleared job {job_id} from DLQ",
                    "job_id": job_id,
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to clear job from DLQ")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing DLQ job {job_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear job from DLQ")


@dlq_router.get("/stats", response_model=DLQStatsResponse)
async def get_dlq_stats():
    """
    Get comprehensive statistics about the Dead Letter Queue.

    Includes job counts by function and error type, age statistics, and retry patterns.
    """
    try:
        stats = queue_client.get_dlq_stats()

        return DLQStatsResponse(
            total_failed_jobs=stats.get("total_failed_jobs", 0),
            batch_processing_failed=stats.get("batch_processing_failed", 0),
            report_generation_failed=stats.get("report_generation_failed", 0),
        )

    except Exception as e:
        logger.error(f"Error getting DLQ stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve DLQ statistics")


@dlq_router.post("/bulk-requeue")
async def bulk_requeue_dlq_jobs(
    function_name: Optional[str] = Query(
        None, description="Only requeue jobs from this function"
    ),
    error_type: Optional[str] = Query(
        None, description="Only requeue jobs with this error type"
    ),
    max_jobs: int = Query(
        10, ge=1, le=100, description="Maximum number of jobs to requeue"
    ),
):
    """
    Bulk requeue multiple failed jobs from the DLQ based on filters.

    Useful for requeuing jobs after fixing systemic issues.
    """
    try:
        jobs = queue_client.get_dlq_jobs(
            limit=max_jobs * 2
        )  # Get more to account for filtering

        requeued_jobs = []
        failed_jobs = []

        for job_info in jobs:
            # Apply filters
            if function_name and job_info.function_name != function_name:
                continue
            if error_type and job_info.error_type != error_type:
                continue

            # Check if we've reached the limit
            if len(requeued_jobs) >= max_jobs:
                break

            # Check if job can be requeued
            if not job_info.function_name:
                failed_jobs.append(
                    {"job_id": job_info.job_id, "reason": "Missing function name"}
                )
                continue

            if _is_retries_exhausted(job_info.metadata):
                failed_jobs.append(
                    {"job_id": job_info.job_id, "reason": "Maximum retries exceeded"}
                )
                continue

            # Attempt to requeue
            success = queue_client.requeue_dlq_job(job_info.job_id)
            if success:
                requeued_jobs.append(job_info.job_id)
            else:
                failed_jobs.append(
                    {"job_id": job_info.job_id, "reason": "Requeue operation failed"}
                )

        return JSONResponse(
            {
                "message": f"Bulk requeue completed: {len(requeued_jobs)} successful, {len(failed_jobs)} failed",
                "requeued_jobs": requeued_jobs,
                "failed_jobs": failed_jobs,
                "filters_applied": {
                    "function_name": function_name,
                    "error_type": error_type,
                    "max_jobs": max_jobs,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error in bulk requeue: {e}")
        raise HTTPException(status_code=500, detail="Failed to perform bulk requeue")
