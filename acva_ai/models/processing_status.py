from datetime import datetime, timezone
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class PipelineStageStatus(BaseModel):
    """
    Represents the status of a pipeline audio_processing stage
    """

    stage_name: str
    status: Literal["not_started", "in_progress", "completed", "failed"] = "not_started"
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error: Optional[str] = None
    duration_seconds: Optional[float] = None
    attempts: int = 0

    class Config:
        json_encoders = {datetime: lambda dt: dt.isoformat() if dt else None}


class PipelineError(BaseModel):
    """
    Represents an error that occurred during pipeline audio_processing
    """

    stage: str
    error_message: str
    timestamp: datetime
    error_type: str
    stack_trace: Optional[str] = None
    additional_info: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {datetime: lambda dt: dt.isoformat() if dt else None}


class StageResult(BaseModel):
    """
    Represents the result of a pipeline stage, including partial results
    """

    is_complete: bool = False
    partial_result: Optional[Any] = None
    error: Optional[str] = None
    timestamp: Optional[datetime] = None

    class Config:
        json_encoders = {datetime: lambda dt: dt.isoformat() if dt else None}


class ProcessingStatus(BaseModel):
    """
    Comprehensive model for tracking pipeline audio_processing status
    """

    # Basic Information
    task_id: str
    overall_status: Literal[
        "queued", "started", "audio_processing", "completed", "failed"
    ] = "queued"
    start_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    end_time: Optional[datetime] = None

    # Stage tracking
    current_stage: Optional[str] = None
    stage_statuses: Dict[str, PipelineStageStatus] = Field(default_factory=dict)
    stage_results: Dict[str, StageResult] = Field(default_factory=dict)

    # Error tracking
    errors: List[PipelineError] = Field(default_factory=list)
    last_error: Optional[str] = None

    # Performance tracking
    stage_timings: Dict[str, float] = Field(default_factory=dict)
    total_processing_time: Optional[float] = None

    # Metadata
    successful_stages: List[str] = Field(default_factory=list)
    failed_stages: List[str] = Field(default_factory=list)

    class Config:
        json_encoders = {datetime: lambda dt: dt.isoformat() if dt else None}

    def dict(self, *args, **kwargs):
        """Override dict method to ensure datetime serialization"""
        d = super().dict(*args, **kwargs)
        # Convert datetime objects to ISO format strings
        if d.get("start_time"):
            d["start_time"] = d["start_time"].isoformat()
        if d.get("end_time"):
            d["end_time"] = d["end_time"].isoformat()
        return d

    def start_stage(self, stage_name: str) -> None:
        """Start tracking a new pipeline stage"""
        self.current_stage = stage_name
        if stage_name not in self.stage_statuses:
            self.stage_statuses[stage_name] = PipelineStageStatus(
                stage_name=stage_name,
                status="in_progress",
                start_time=datetime.now(timezone.utc),
                attempts=1,
            )
        else:
            self.stage_statuses[stage_name].status = "in_progress"
            self.stage_statuses[stage_name].start_time = datetime.now(timezone.utc)
            self.stage_statuses[stage_name].attempts += 1

        # Initialize stage result if not exists
        if stage_name not in self.stage_results:
            self.stage_results[stage_name] = StageResult()

    def complete_stage(self, stage_name: str, result: Any = None) -> None:
        """Mark a pipeline stage as completed with its result"""
        if stage_name in self.stage_statuses:
            end_time = datetime.now(timezone.utc)
            self.stage_statuses[stage_name].status = "completed"
            self.stage_statuses[stage_name].end_time = end_time
            start_time = self.stage_statuses[stage_name].start_time
            if start_time is not None:
                duration = (end_time - start_time).total_seconds()
            else:
                duration = 0
            self.stage_statuses[stage_name].duration_seconds = duration

            # Update stage result
            self.stage_results[stage_name].is_complete = True
            self.stage_results[stage_name].partial_result = result
            self.stage_results[stage_name].timestamp = end_time

            # Update tracking
            self.stage_timings[stage_name] = duration
            if stage_name not in self.successful_stages:
                self.successful_stages.append(stage_name)

    def fail_stage(
        self, stage_name: str, error: Exception, stack_trace: Optional[str] = None
    ) -> None:
        """Mark a pipeline stage as failed and record the error"""
        if stage_name in self.stage_statuses:
            end_time = datetime.now(timezone.utc)
            self.stage_statuses[stage_name].status = "failed"
            self.stage_statuses[stage_name].end_time = end_time
            self.stage_statuses[stage_name].error = str(error)
            start_time = self.stage_statuses[stage_name].start_time
            if start_time is not None:
                duration = (end_time - start_time).total_seconds()
            else:
                duration = 0
            self.stage_statuses[stage_name].duration_seconds = duration
            self.stage_timings[stage_name] = duration

        # Record the error
        stage_attempts = 0
        if stage_name in self.stage_statuses:
            stage_attempts = self.stage_statuses[stage_name].attempts

        error_details = PipelineError(
            stage=stage_name,
            error_message=str(error),
            timestamp=datetime.now(timezone.utc),
            error_type=type(error).__name__,
            stack_trace=stack_trace,
            additional_info={
                "stage_attempts": stage_attempts,
            },
        )
        self.errors.append(error_details)
        self.last_error = str(error)

        # Update stage result to preserve partial progress
        if stage_name in self.stage_results:
            self.stage_results[stage_name].error = str(error)
            self.stage_results[stage_name].timestamp = datetime.now(timezone.utc)

        # Update tracking
        if stage_name not in self.failed_stages:
            self.failed_stages.append(stage_name)

    def finalize(self) -> None:
        """Mark audio_processing as complete and calculate final metrics"""
        self.end_time = datetime.now(timezone.utc)
        self.total_processing_time = (self.end_time - self.start_time).total_seconds()

        # Determine final status
        if len(self.failed_stages) == 0:
            self.overall_status = "completed"
        elif len(self.successful_stages) > 0:
            self.overall_status = "completed"  # Completed with some failures
        else:
            self.overall_status = "failed"
