"""
Job Metadata Models

This module provides structured Pydantic models for job metadata,
replacing loose dictionary structures with type-safe objects.
"""

from __future__ import annotations

from datetime import datetime, timezone
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class BaseJobMetadata(BaseModel):
    """Base metadata for all jobs."""

    job_type: str = Field(..., description="Type of job being executed")
    created_by: str = Field(
        ..., description="Service or component that created the job"
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the job metadata was created",
    )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for RQ metadata storage."""
        return self.model_dump(mode="json")

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BaseJobMetadata":
        """Create from dictionary format."""
        return cls(**data)


class BatchProcessingJobMetadata(BaseJobMetadata):
    """Metadata for batch processing jobs."""

    job_type: str = Field(default="batch_processing", description="Job type identifier")

    # Visit and batch identifiers
    visit_job_id: str = Field(..., description="Visit job ID this batch belongs to")
    batch_id: str = Field(..., description="Unique batch identifier")
    batch_number: int = Field(..., description="Sequential batch number within visit")

    # File processing information
    file_count: int = Field(..., description="Number of files in this batch")
    total_size_bytes: int = Field(..., description="Total size of all files in bytes")

    # Provider configuration
    llm_provider: str = Field(..., description="LLM provider being used")
    transcript_provider: str = Field(..., description="Transcript provider being used")
    diarize: bool = Field(..., description="Whether diarization is enabled")

    @property
    def total_size_mb(self) -> float:
        """Get total size in MB."""
        return self.total_size_bytes / (1024 * 1024)


class ReportGenerationJobMetadata(BaseJobMetadata):
    """Metadata for report generation jobs."""

    job_type: str = Field(
        default="report_generation", description="Job type identifier"
    )

    # Visit information
    visit_job_id: str = Field(..., description="Visit job ID for report generation")

    # Batch information
    total_batches: int = Field(..., description="Total number of batches in visit")
    completed_batches: int = Field(..., description="Number of completed batches")

    # Provider configuration
    llm_provider: str = Field(..., description="LLM provider being used")
    transcript_provider: str = Field(..., description="Transcript provider being used")
    diarize: bool = Field(..., description="Whether diarization is enabled")

    # Assembly information
    assembly_started_at: Optional[datetime] = Field(
        default=None, description="When batch assembly started"
    )
    assembly_duration: Optional[float] = Field(
        default=None, description="Duration of assembly in seconds"
    )

    @property
    def completion_percentage(self) -> float:
        """Calculate completion percentage based on batches."""
        if self.total_batches == 0:
            return 0.0
        return (self.completed_batches / self.total_batches) * 100


class JobExecutionMetadata(BaseModel):
    """Runtime execution metadata added by BaseJob during processing."""

    # Execution timing
    execution_started_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When job execution started",
    )
    execution_ended_at: Optional[datetime] = Field(
        default=None, description="When job execution ended"
    )
    execution_duration: Optional[float] = Field(
        default=None, description="Total execution duration in seconds"
    )

    # Progress tracking
    progress_percentage: int = Field(default=0, description="Job progress percentage")
    progress_message: str = Field(default="", description="Current progress message")

    # Retry information
    retry_info: Optional[RetryInfo] = Field(
        default=None, description="Detailed retry information"
    )

    # Status tracking
    status: str = Field(default="running", description="Current execution status")

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for RQ metadata storage."""
        return self.model_dump(mode="json")

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "JobExecutionMetadata":
        """Create from dictionary format."""
        return cls(**data)


class RetryInfo(BaseModel):
    """Structured retry information used in execution metadata."""

    retries_left: Optional[int] = None
    retry_intervals: Optional[Any] = None
    started_at: Optional[str] = None
    created_at: Optional[str] = None
    enqueued_at: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True


class JobMetadata(BaseModel):
    """Combined metadata structure containing both job-specific and execution metadata."""

    job_metadata: BaseJobMetadata = Field(..., description="Job-specific metadata")
    execution_metadata: JobExecutionMetadata = Field(
        default_factory=lambda: JobExecutionMetadata(),
        description="Runtime execution metadata",
    )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for RQ metadata storage, maintaining structure."""
        return {
            "job_metadata": self.job_metadata.to_dict(),
            "execution_metadata": self.execution_metadata.to_dict(),
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> JobMetadata:
        """Create from dictionary format supporting both nested and flat structures."""
        job_data = data["job_metadata"]
        execution_data = data["execution_metadata"]
        # Determine job metadata type based on job_type
        job_type = job_data.get("job_type", "base")
        if job_type == "batch_processing":
            job_metadata = BatchProcessingJobMetadata(**job_data)
        elif job_type == "report_generation":
            job_metadata = ReportGenerationJobMetadata(**job_data)
        else:
            job_metadata = BaseJobMetadata(**job_data)

        execution_metadata = JobExecutionMetadata(**execution_data)

        return cls(job_metadata=job_metadata, execution_metadata=execution_metadata)
