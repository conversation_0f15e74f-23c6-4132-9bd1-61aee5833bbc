import logging
from typing import List, Optional

import Levenshtein

from acva_ai.llm.helpers import parse_json_output
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.services.rag import rag_service
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)

EXTRACT_MEDICATIONS_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 
Your task is to extract all the references to medications, drugs, prescription medicine, including names of the medications.

Because this is a transcript from a speech-to-text software we want to make sure that all the names of medications are correctly identified, even if they are not spelled correctly or if there are minor errors in the transcript.

You must extract all the named medications or related text (even if a common medication is discussed without any particular name)

This is the transcript:
{transcript}

Extract all the references to medications, drugs, prescription medicine. Include only one mention per medication entity.

Respond in {language} 

Make a list of all the medications mentioned in the transcript in a JSON format, with the following structure:
JSON:
- medication_name: <name of the medication in {language}> if spelled, if not, None
- medication_context: <context / explanations in which the medication is mentioned, the symptoms for which is prescribed or any other relevant information about that particular medication in the discussion>

Return the JSON list without any other commentary.
"""

CHECK_MEDICATION_NAME_RAG = """
This is a context in {language} from a medical visit, generated by a speech-to-text system that may contain transcription errors such as misspelled or phonetically similar words.

Your task is to identify and extract the correct medication name, choosing the most contextually appropriate match from the provided list of candidate medications. Use the surrounding context {context} , semantic similarity to guide your choice and the medication name that we identified {medication_name}.

The list of candidate medications is:
{list_of_medication}

Their corresponding relevance scores from a RAG-based retrieval system are:
{list_of_scores}

Use the RAG scores as a *secondary signal* — they may influence your decision, but the *primary criterion* should be the context and the candidate medications

Respond with:
- The **medication name**: the most likely correct medication name from the list.
- In **{language}**, without any commentary, explanation, or formatting.

If no candidate fits the context with sufficient confidence, return an empty string in this format "".
"""


async def _get_medication_from_rag(
    medication_name: str,
    context: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
) -> str:
    rag_medication = await rag_service.query(
        collection_name="nomenclatura",
        query_text=medication_name,
        score_threshold=0.5,
        limit=20,
    )

    levenshtein_distances = []
    for result in rag_medication:
        try:
            if "payload" in result and "medicament" in result["payload"]:
                distance = Levenshtein.distance(
                    medication_name, result["payload"]["medicament"]
                )
                levenshtein_distances.append(distance)
            else:
                logger.warning(f"Missing medicament field in result payload: {result}")
                levenshtein_distances.append(
                    len(medication_name)
                )  # Use max distance as fallback
        except Exception as e:
            logger.warning(f"Error computing Levenshtein distance: {e}")
            levenshtein_distances.append(len(medication_name))

    if levenshtein_distances:
        min_dist, max_dist = min(levenshtein_distances), max(levenshtein_distances)
        range_dist = max_dist - min_dist or 1e-9
        levenshtein_similarities = [
            1 - (dist - min_dist) / range_dist for dist in levenshtein_distances
        ]
    else:
        levenshtein_similarities = []

    weight_embedding, weight_levenshtein = 0.2, 0.8
    scored_results = []
    for result, lev_sim in zip(rag_medication, levenshtein_similarities):
        aggregated_score = (
            weight_embedding * result["score"] + weight_levenshtein * lev_sim
        )
        result["score"] = aggregated_score
        scored_results.append(result)

    ranked_results = sorted(scored_results, key=lambda r: r["score"], reverse=True)

    list_of_medication = [
        medication["payload"]["medicament"] for medication in ranked_results
    ]
    list_of_scores = [score["score"] for score in ranked_results]

    extraction_prompt = CHECK_MEDICATION_NAME_RAG.format(
        list_of_medication=list_of_medication,
        language=language,
        context=context,
        list_of_scores=list_of_scores,
        medication_name=medication_name,
    )
    response = await llm_orchestrator.call_llm(
        prompt=extraction_prompt, response_usage=None
    )
    return response


async def extract_medication(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> List:
    """
    Extract medications from a transcript using an LLM.

    Args:
        transcript: The transcript to process
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls

    Returns:
        List of extracted medications
    """
    check_prompt = EXTRACT_MEDICATIONS_PROMPT.format(
        transcript=transcript, language=language
    )

    response = await llm_orchestrator.call_llm(
        prompt=check_prompt, response_usage=response_usage
    )

    response_dict = await parse_json_output(response)

    results = []
    for item in response_dict or []:
        name = item.get("medication_name", None)
        context = item.get("medication_context", None)
        extracted_name = await _get_medication_from_rag(
            medication_name=name.lower() if name else "",
            context=context,
            llm_orchestrator=llm_orchestrator,
            language=language,
        )
        results.append([name, context, extracted_name])

    return results
