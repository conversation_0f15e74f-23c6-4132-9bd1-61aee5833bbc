import logging
from typing import List, Optional

from acva_ai.llm.helpers import parse_json_output
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.services.rag import rag_service
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)

EXTRACT_MEDICATIONS_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 
Your task is to extract all the references to medications, drugs, prescription medicine, including names of the medications.

Because this is a transcript from a speech-to-text software we want to make sure that all the names of medications are correctly identified, even if they are not spelled correctly or if there are minor errors in the transcript.

You must extract all the named medications or related text (even if a common medication is discussed without any particular name)

This is the transcript:
{transcript}

Extract all the references to medications, drugs, prescription medicine. Include only one mention per medication entity.

Respond in {language} 

Make a list of all the medications mentioned in the transcript in a JSON format, with the following structure:
JSON:
- medication_name: <name of the medication in {language}> if spelled, if not, None
- medication_context: <context / explanations in which the medication is mentioned, the symptoms for which is prescribed or any other relevant information about that particular medication in the discussion>

Return the JSON list without any other commentary.
"""

CHECK_MEDICATION_NAME_RAG = """
This is a context in {language} from a medical visit, generated by a speech-to-text system that may contain transcription errors such as misspelled or phonetically similar words.

Your task is to identify and extract the correct medication name, choosing the most contextually appropriate match from the provided list of candidate medications. Use both the surrounding context {context} and semantic similarity to guide your choice.

The list of candidate medications is:
{list_of_medication}

Their corresponding relevance scores from a RAG-based retrieval system are:
{list_of_scores}

Use the RAG scores as a *secondary signal* — they may influence your decision, but the *primary criterion* should be the context and the candidate medications

Respond with:
- The **medication name**: the most likely correct medication name from the list.
- In **{language}**, without any commentary, explanation, or formatting.

If no candidate fits the context with sufficient confidence, return an empty string in this format "".
"""


async def _get_medication_from_rag(
    medication_name: str,
    context: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
) -> str:
    rag_medication = await rag_service.query(
        collection_name="nomenclatura",
        query_text=medication_name,
        score_threshold=0.5,
        limit=2,
    )
    list_of_medication = [
        medication["payload"]["medicament"] for medication in rag_medication
    ]
    list_of_scores = [score["score"] for score in rag_medication]
    extraction_prompt = CHECK_MEDICATION_NAME_RAG.format(
        list_of_medication=list_of_medication,
        language=language,
        context=context,
        list_of_scores=list_of_scores,
    )
    response = await llm_orchestrator.call_llm(
        prompt=extraction_prompt, response_usage=None
    )
    return response


async def extract_medication(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> List:
    """
    Extract medications from a transcript using an LLM.

    Args:
        transcript: The transcript to process
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls

    Returns:
        List of extracted medications
    """
    check_prompt = EXTRACT_MEDICATIONS_PROMPT.format(
        transcript=transcript, language=language
    )

    response = await llm_orchestrator.call_llm(
        prompt=check_prompt, response_usage=response_usage
    )

    response_dict = await parse_json_output(response)

    results = []
    for item in response_dict:
        name = item.get("medication_name", None)
        context = item.get("medication_context", None)
        extracted_name = await _get_medication_from_rag(
            medication_name=name.lower() if name else "",
            context=context,
            llm_orchestrator=llm_orchestrator,
            language=language,
        )
        results.append([name, context, extracted_name])

    return results
