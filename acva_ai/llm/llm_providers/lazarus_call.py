import asyncio
import logging
from typing import Dict, List, Optional, Union

import aiohttp
from lazarus_ai import Lazarus<PERSON><PERSON>

from acva_ai._params import (
    LAZARUS_AUTHKEY,
    LAZARUS_MODEL_ID,
    LAZARUS_ORGID,
    LAZARUS_PRICING,
)
from acva_ai.llm.llm_providers.llm_helpers import (
    LLMProviderError,
    LLMRateLimitError,
    calculate_usage_cost,
    estimate_tokens_from_text,
    extract_task_id_from_prompt,
    generate_cache_filepath,
    get_log_prefix,
    load_cached_response,
    save_to_cache,
)
from acva_ai.llm.llm_providers.llm_provider import LLMProvider
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


# TODO Flavius: Lazarus should not be used with the Riky2 model. We need to use RikAI which only uspoprt pdf as input + prompt
# TODO Outdated
class LazarusProvider:
    """Lazarus AI LLM provider class."""

    def __init__(self):
        """Initialize the Lazarus provider."""
        self.provider_name = "lazarus"
        self.model_id = LAZARUS_MODEL_ID

        if not LAZARUS_AUTHKEY:
            raise Exception("LAZARUS_AUTHKEY environment variable is not set")

    async def call_llm(
        self,
        prompt: str,
        max_tokens: int = 5000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        current_retry: int = 0,
        max_retries: int = 3,
        retry_delay: float = 10,
        temperature: float = 0,
        custom_data: Optional[Dict] = None,
        webhook_url: Optional[str] = None,
    ) -> str:
        """
        Asynchronous call to Lazarus AI API with caching.

        Args:
            prompt: The prompt or list of prompts to send to the model
            model_id: The Lazarus model ID (default: "riky2")
            max_tokens: Maximum tokens to generate (for consistency, not used by Lazarus)
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            current_retry: Current retry attempt (used internally)
            max_retries: Maximum number of retries for errors
            retry_delay: Initial delay before retrying (will increase exponentially)
            temperature: Temperature parameter (for consistency, not used by Lazarus)
            custom_data: Optional custom JSON to include in the response
            webhook_url: Optional webhook URL for asynchronous processing

        Returns:
            The model's response as a string
        """
        task_id = extract_task_id_from_prompt(prompt)
        log_prefix = get_log_prefix(task_id)

        # Check cache first
        cache_filepath = generate_cache_filepath(
            prompt, self.model_id, self.provider_name, max_tokens, temperature
        )
        if use_cache:
            cached_data = load_cached_response(cache_filepath)
            if cached_data:
                logger.info(f"{log_prefix}Loaded Lazarus response from cache.")
                # Handle both old and new cache formats
                if "response" in cached_data:
                    return cached_data["response"]
                elif "data" in cached_data and cached_data["data"]:
                    return cached_data["data"][0]["answer"]

        # Initialize Lazarus authentication
        auth = LazarusAuth(LAZARUS_ORGID, LAZARUS_AUTHKEY)

        # Use the chat endpoint directly since the lazarus-ai package doesn't support chat
        url = f"https://api.lazarusai.com/api/rikai/chat/{self.model_id}"
        headers = auth.headers  # Use the headers from the auth object

        # Prepare the data payload - Lazarus expects a list of questions
        data = {"question": [prompt]}

        # Add webhook if provided
        if webhook_url:
            data["webhook"] = webhook_url

        # Add custom data if provided
        if custom_data:
            data["custom"] = custom_data

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    if response.status == 200:
                        response_data = await response.json()

                        # Extract the response text
                        response_msg = response_data["data"][0]["answer"]

                        # Track usage if provided
                        if response_usage is not None:
                            # Lazarus doesn't provide token counts, so we estimate
                            input_tokens = estimate_tokens_from_text(str(prompt))
                            output_tokens = estimate_tokens_from_text(response_msg)

                            # Calculate and track usage cost
                            calculate_usage_cost(
                                model_id=self.model_id,
                                input_tokens=input_tokens,
                                output_tokens=output_tokens,
                                pricing_dict=LAZARUS_PRICING,
                                response_usage=response_usage,
                            )

                        # Prepare cache data
                        cache_data = {
                            "response": response_msg,
                            "model_id": self.model_id,
                            "provider": self.provider_name,
                            "raw_response": response_data,
                        }

                        # Cache the response
                        save_to_cache(cache_filepath, cache_data)

                        return response_msg
                    elif response.status == 429:
                        # Rate limit error
                        raise LLMRateLimitError(
                            f"Lazarus rate limit exceeded: {response.status}"
                        )
                    else:
                        error_text = await response.text()
                        raise LLMProviderError(
                            f"Lazarus API error {response.status}: {error_text}"
                        )

        except (LLMRateLimitError, LLMProviderError):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            # Handle retries for other errors
            if current_retry < max_retries:
                wait_time = retry_delay * (2**current_retry)
                logger.warning(
                    f"{log_prefix}Lazarus API error: {str(e)}. Waiting {wait_time} seconds..."
                )
                await asyncio.sleep(wait_time)
                return await self.call_llm(
                    prompt=prompt,
                    max_tokens=max_tokens,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    current_retry=current_retry + 1,
                    max_retries=max_retries,
                    retry_delay=retry_delay,
                    temperature=temperature,
                    custom_data=custom_data,
                    webhook_url=webhook_url,
                )
            else:
                # If we've exhausted retries
                logger.error(
                    f"{log_prefix}Error maintained after {max_retries} retries: {str(e)}"
                )
                raise LLMProviderError(f"Lazarus provider error: {str(e)}")


def test():
    """Test the Lazarus API call function."""
    response_usage = ResponseUsage()

    lazarus = LazarusProvider()

    try:
        # Test with a simple prompt
        result = asyncio.run(
            lazarus.call_llm(
                prompt="What is the capital of France?",
                response_usage=response_usage,
                use_cache=False,  # Disable caching
            )
        )
        print("API Response:")
        print(result)
        print(f"Usage: {response_usage}")

        # Test with a list of prompts
        result_list = asyncio.run(
            lazarus.call_llm(
                prompt=[
                    "What is the capital of France?",
                    "What is the capital of Italy?",
                ],
                response_usage=response_usage,
                use_cache=True,  # Enable caching
            )
        )
        print("\nList Prompt Response:")
        print(result_list)
        print(f"Usage: {response_usage}")

    except Exception as e:
        print(f"Error during test: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test()
