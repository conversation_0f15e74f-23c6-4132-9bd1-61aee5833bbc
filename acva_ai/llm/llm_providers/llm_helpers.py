"""
Common helper functions and utilities for LLM providers.
"""

import hashlib
import json
import logging
import os
import re
from typing import Dict, List, Optional, Union

from acva_ai._params import CACHE_DIR
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)


class LLMError(Exception):
    """Custom exception for LLM-related errors."""

    pass


class LLMProviderError(Exception):
    """Custom exception for LLM provider-specific errors."""

    pass


class LLMRateLimitError(Exception):
    """Custom exception for LLM rate limit errors."""

    pass


def extract_task_id_from_prompt(prompt: Union[str, List[Dict]]) -> Optional[str]:
    """
    Extract task ID from prompt for logging purposes.

    Args:
        prompt: The prompt (string) or messages (list of dicts)

    Returns:
        Task ID if found, None otherwise
    """
    if isinstance(prompt, str):
        task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
        if task_id_match:
            return task_id_match.group(1)
    elif isinstance(prompt, list) and prompt:
        # Check the first message content for task ID
        first_message = prompt[0]
        if isinstance(first_message, dict) and "content" in first_message:
            task_id_match = re.search(
                r"\[Task ([a-f0-9-]+)\]", first_message["content"]
            )
            if task_id_match:
                return task_id_match.group(1)
    return None


def generate_cache_key(
    prompt: Union[str, List[Dict]],
    model_id: str,
    max_tokens: int = 5000,
    temperature: float = 0,
) -> str:
    """
    Generate a cache key for LLM requests.

    Args:
        prompt: The prompt or list of messages
        model_id: The model ID
        max_tokens: Maximum tokens to generate
        temperature: Temperature parameter

    Returns:
        Cache key string
    """
    # Convert to string for hashing
    prompt_str = (
        json.dumps(prompt, sort_keys=True) if isinstance(prompt, list) else prompt
    )

    # Create a unique hash based on all parameters
    hash_input = f"{prompt_str}_{model_id}_{max_tokens}_{temperature}"
    content_hash = hashlib.md5(hash_input.encode("utf-8")).hexdigest()
    return f"llm_{content_hash}_{model_id}"


def generate_cache_filepath(
    prompt: Union[str, List[Dict]],
    model_id: str,
    provider_name: str,
    max_tokens: int = 5000,
    temperature: float = 0,
) -> str:
    """
    Generate a cache file path for LLM requests.

    Args:
        prompt: The prompt or list of messages
        model_id: The model ID
        provider_name: Name of the provider (for cache directory)
        max_tokens: Maximum tokens to generate
        temperature: Temperature parameter

    Returns:
        Full path to cache file
    """
    cache_key = generate_cache_key(prompt, model_id, max_tokens, temperature)
    cache_filename = f"{cache_key}.json"

    # Create provider-specific cache directory
    provider_cache_dir = os.path.join(CACHE_DIR, provider_name)
    os.makedirs(provider_cache_dir, exist_ok=True)

    return os.path.join(provider_cache_dir, cache_filename)


def save_to_cache(cache_filepath: str, response_data: Dict) -> None:
    """
    Save response to cache.

    Args:
        cache_filepath: Path to cache file
        response_data: Response data to cache
    """
    try:
        os.makedirs(os.path.dirname(cache_filepath), exist_ok=True)
        with open(cache_filepath, "w", encoding="utf-8") as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)
    except (IOError, OSError) as e:
        logger.warning(f"Error writing to cache file: {e}")


def load_cached_response(cache_filepath: str) -> Optional[Dict]:
    """
    Load cached response if available.

    Args:
        cache_filepath: Path to cache file

    Returns:
        Cached response data if available, None otherwise
    """
    try:
        if os.path.isfile(cache_filepath):
            with open(cache_filepath, "r", encoding="utf-8") as f:
                return json.load(f)
    except (json.JSONDecodeError, IOError, OSError) as e:
        logger.warning(f"Error reading cache file: {e}")
    return None


def standardize_response(response_data: Dict, provider_name: str) -> Dict:
    """
    Standardize response format across providers.

    Args:
        response_data: Raw response data from provider
        provider_name: Name of the provider

    Returns:
        Standardized response dictionary
    """
    # Ensure all responses have at least these fields
    standardized = {
        "text": response_data.get("response", response_data.get("text", "")),
        "provider": provider_name,
        "model_id": response_data.get("model_id", ""),
    }

    # Preserve original response data
    standardized["raw_response"] = response_data

    # Include usage information if available
    if "usage" in response_data:
        standardized["usage"] = response_data["usage"]

    return standardized


def calculate_usage_cost(
    model_id: str,
    input_tokens: int,
    output_tokens: int,
    pricing_dict: Dict[str, Dict[str, float]],
    response_usage: Optional[ResponseUsage] = None,
) -> Optional[float]:
    """
    Calculate and track usage costs for LLM requests.

    Args:
        model_id: The model ID
        input_tokens: Number of input tokens
        output_tokens: Number of output tokens
        pricing_dict: Pricing dictionary for the provider
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Total cost if pricing is available, None otherwise
    """
    cost = None
    model_pricing_dict = pricing_dict.get(model_id)

    if model_pricing_dict is not None:
        input_cost = model_pricing_dict["input"] * input_tokens * 1e-6
        output_cost = model_pricing_dict["output"] * output_tokens * 1e-6
        cost = input_cost + output_cost

    # Track usage if provided
    if response_usage is not None:
        llm_usage = LLMUsage(
            model_id=model_id,
            cost=cost,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
        )
        response_usage.add_llm_usage(llm_usage)

    return cost


def estimate_tokens_from_text(text: str, multiplier: float = 1.3) -> int:
    """
    Estimate token count from text (for providers that don't return token counts).

    Args:
        text: The text to estimate tokens for
        multiplier: Multiplier to apply to word count (default 1.3)

    Returns:
        Estimated token count
    """
    return int(len(str(text).split()) * multiplier)


def prepare_messages_from_prompt(prompt: Union[str, List[Dict]]) -> List[Dict]:
    """
    Prepare messages list from prompt for chat-based APIs.

    Args:
        prompt: The prompt (string) or messages (list of dicts)

    Returns:
        List of message dictionaries
    """
    if isinstance(prompt, str):
        return [{"role": "system", "content": prompt}]
    elif isinstance(prompt, list):
        return prompt
    else:
        raise LLMError(f"Invalid prompt type: {type(prompt)}")


def validate_response_format(response_data: Dict, required_fields: List[str]) -> None:
    """
    Validate that response data contains required fields.

    Args:
        response_data: Response data to validate
        required_fields: List of required field names

    Raises:
        LLMProviderError: If required fields are missing
    """
    missing_fields = [field for field in required_fields if field not in response_data]
    if missing_fields:
        raise LLMProviderError(f"Missing required fields in response: {missing_fields}")


def get_log_prefix(task_id: Optional[str]) -> str:
    """
    Get log prefix for consistent logging.

    Args:
        task_id: Optional task ID

    Returns:
        Log prefix string
    """
    return f"[Task {task_id}] " if task_id else ""
