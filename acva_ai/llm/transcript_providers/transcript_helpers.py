"""
Common helper functions and utilities for transcription providers.
"""

import hashlib
import json
import os
from typing import Dict, Optional

from pydub import AudioSegment

from acva_ai.utils.general_utils import calculate_cost
from acva_ai.utils.usage import LLMUsage, ResponseUsage


class AudioTranscriptionError(Exception):
    """Custom exception for audio transcription errors."""

    pass


class AudioFileError(Exception):
    """Custom exception for audio file related errors."""

    pass


class TranscriptionResponse:
    """
    Response object for transcription that can contain both text and speaker data.

    This class behaves like a string when used in string contexts for backward
    compatibility, but can also contain additional speaker diarization data when
    available.
    """

    def __init__(self, text: str, speakers_data: Optional[Dict] = None):
        """
        Initialize transcription response.

        Args:
            text: The transcribed text
            speakers_data: Optional dictionary containing speaker diarization data
        """
        self.text = text
        self.speakers_data = speakers_data

    def __str__(self) -> str:
        """Return the text content when used as a string."""
        return self.text

    def __repr__(self) -> str:
        """Return a detailed representation of the response."""
        if self.speakers_data:
            return (
                f"TranscriptionResponse(text='{self.text[:50]}...', has_speakers=True)"
            )
        return f"TranscriptionResponse(text='{self.text[:50]}...')"

    def __eq__(self, other) -> bool:
        """Allow comparison with strings and other TranscriptionResponse objects."""
        if isinstance(other, str):
            return self.text == other
        elif isinstance(other, TranscriptionResponse):
            return self.text == other.text and self.speakers_data == other.speakers_data
        return False

    def __len__(self) -> int:
        """Return the length of the text."""
        return len(self.text)

    def __bool__(self) -> bool:
        """Return True if text is not empty."""
        return bool(self.text)

    def has_speakers(self) -> bool:
        """Check if speaker diarization data is available."""
        return self.speakers_data is not None

    def get_speakers(self) -> Optional[Dict]:
        """Get the speaker diarization data."""
        return self.speakers_data

    def get_speaker_sentences(self, speaker_id: str) -> list:
        """
        Get all sentences for a specific speaker.

        Args:
            speaker_id: The speaker ID to get sentences for

        Returns:
            List of sentence dictionaries for the speaker
        """
        if not self.speakers_data:
            return []
        return self.speakers_data.get(speaker_id, [])

    def get_all_speakers(self) -> list:
        """Get a list of all speaker IDs."""
        if not self.speakers_data:
            return []
        return list(self.speakers_data.keys())

    def get(self, key: str, default=None):
        """
        Dictionary-style access for backward compatibility.

        Args:
            key: The key to access ('text', 'speakers_data', etc.)
            default: Default value if key not found

        Returns:
            The requested value or default
        """
        if key == "text":
            return self.text
        elif key == "speakers_data":
            return self.speakers_data
        elif key == "sentences_by_speaker":
            return self.speakers_data
        else:
            return default

    def to_dict(self) -> Dict:
        """
        Convert the TranscriptionResponse to a dictionary for JSON serialization.

        Returns:
            Dictionary containing text and speakers_data
        """
        return {"text": self.text, "speakers_data": self.speakers_data}


def get_audio_duration_from_file(audio_file_path: str) -> float:
    """Get audio duration in seconds from file path."""
    try:
        audio = AudioSegment.from_file(audio_file_path)
        return len(audio) / 1000  # pydub duration is in milliseconds
    except Exception as e:
        raise AudioFileError(f"Error getting duration from file: {e}")


def get_audio_duration_from_segment(audio_segment: AudioSegment) -> float:
    """Get audio duration in seconds from AudioSegment."""
    return len(audio_segment) / 1000  # pydub duration is in milliseconds


def validate_file_size(file_path: str, max_size_mb: float) -> None:
    """Validate that the file is within the size limit."""
    try:
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        if size_mb > max_size_mb:
            raise AudioFileError(
                f"Audio file too large: {size_mb:.2f} MB exceeds limit of {max_size_mb} MB"
            )
    except OSError as e:
        raise AudioFileError(f"Error checking file size: {e}")


def get_file_size_mb(file_path: str) -> float:
    """Get file size in megabytes."""
    try:
        return os.path.getsize(file_path) / (1024 * 1024)
    except OSError as e:
        raise AudioFileError(f"Error getting file size: {e}")


def validate_audio_file(audio_file_path: str) -> bytes:
    """Validate audio file exists and return its content."""
    if not os.path.exists(audio_file_path):
        raise AudioFileError(f"Audio file not found: {audio_file_path}")

    try:
        with open(audio_file_path, "rb") as f:
            return f.read()
    except Exception as e:
        raise AudioFileError(f"Error reading audio file: {e}")


async def calculate_usage_cost_from_file(
    model_id: str,
    audio_file_path: str,
    response_data: Dict,
    response_usage: ResponseUsage,
) -> None:
    """Calculate and track usage costs for file-based transcription."""
    try:
        audio_duration_seconds = get_audio_duration_from_file(audio_file_path)
        cost = await calculate_cost(
            model=model_id, audio_duration_seconds=audio_duration_seconds
        )

        llm_usage = LLMUsage(
            model_id=model_id,
            cost=cost,
            input_tokens=0,  # Not applicable for audio
            output_tokens=len(
                response_data.get("text", "")
            ),  # Use text length as proxy
            audio_input_duration=audio_duration_seconds,
        )

        response_usage.add_llm_usage(llm_usage)
    except Exception as e:
        print(f"Warning: Error calculating transcription cost: {e}")


async def calculate_usage_cost_from_segment(
    model_id: str,
    audio_segment: AudioSegment,
    response_data: Dict,
    response_usage: ResponseUsage,
) -> None:
    """Calculate and track usage costs for AudioSegment-based transcription."""
    try:
        audio_duration_seconds = get_audio_duration_from_segment(audio_segment)
        cost = await calculate_cost(
            model=model_id, audio_duration_seconds=audio_duration_seconds
        )

        llm_usage = LLMUsage(
            model_id=model_id,
            cost=cost,
            input_tokens=0,  # Not applicable for audio
            output_tokens=len(
                response_data.get("text", "")
            ),  # Use text length as proxy
            audio_input_duration=audio_duration_seconds,
        )

        response_usage.add_llm_usage(llm_usage)
    except Exception as e:
        print(f"Warning: Error calculating transcription cost: {e}")


def standardize_response(response_data: Dict, provider_name: str) -> Dict:
    """
    Standardize response format across providers.

    Args:
        response_data: Raw response data from provider
        provider_name: Name of the provider

    Returns:
        Standardized response dictionary
    """
    # Ensure all responses have at least these fields
    standardized = {
        "text": response_data.get("text", ""),
        "language_code": response_data.get("language"),
        "provider": provider_name,
    }

    # Include sentences with speaker information if available
    if "sentences" in response_data:
        standardized["sentences"] = response_data["sentences"]

    # Include words data if available
    if "words" in response_data:
        standardized["words"] = response_data["words"]

    # Preserve original response data
    standardized["raw_response"] = response_data

    return standardized


def generate_cache_key(content: bytes, language: str, temperature: float = 0) -> str:
    """Generate a cache key for transcription requests."""
    content_hash = hashlib.md5(content).hexdigest()
    return f"transcription_{content_hash}_{language}_{temperature}"


def save_to_cache(cache_filepath: str, response_data: Dict) -> None:
    """Save response to cache."""
    try:
        os.makedirs(os.path.dirname(cache_filepath), exist_ok=True)
        with open(cache_filepath, "w", encoding="utf-8") as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)
    except (IOError, OSError) as e:
        print(f"Error writing to cache file: {e}")


def load_cached_response(cache_filepath: str) -> Optional[Dict]:
    """Load cached response if available."""
    try:
        if os.path.isfile(cache_filepath):
            with open(cache_filepath, "r", encoding="utf-8") as f:
                return json.load(f)
    except (json.JSONDecodeError, IOError, OSError) as e:
        print(f"Error reading cache file: {e}")
    return None
