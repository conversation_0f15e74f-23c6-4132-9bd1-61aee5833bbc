import json
from typing import Dict, Optional

from acva_ai._params import DEFAULT_LLM_PROVIDER
from acva_ai.llm.llm_orchestrator import LLMOrchestrator


async def parse_json_output(
    output: str,
    is_recursion: bool = False,
) -> Optional[Dict]:
    """
    Parses the JSON output from the LLM to load it as a dict.
    """
    output = output.strip()

    if output.lower() == "none":
        return None

    delimiters = [("```json\n", "\n```"), ("```\n", "\n```"), ("```", "```")]

    for delimiter in delimiters:
        if output.startswith(delimiter[0]):
            output = output[len(delimiter[0]) :]
        if output.endswith(delimiter[1]):
            output = output[: -len(delimiter[1])]
    try:
        loaded_dict = json.loads(output)
        return loaded_dict
    except Exception:
        if not is_recursion:
            output = await retry_json_parse(output)
            return await parse_json_output(output, is_recursion=True)
        print(f"Failed to parse LLM response as JSON: {output}.")
        return None


async def parse_python_output(
    output: str,
    is_recursion: bool = False,
) -> Optional[str]:
    """
    Parses the JSON output from the LLM to load it as a dict.
    """
    output = output.strip()

    if output.lower() == "none":
        return None

    delimiters = [("```python\n", "\n```"), ("```\n", "\n```"), ("```", "```")]

    for delimiter in delimiters:
        if output.startswith(delimiter[0]):
            output = output[len(delimiter[0]) :]
        if output.endswith(delimiter[1]):
            output = output[: -len(delimiter[1])]
    try:
        exec(output)
        return output

    except Exception as e:
        if not is_recursion:
            output = await retry_python_parse(output)
            return await parse_python_output(output, is_recursion=True)

        print(f"Failed to parse LLM response as PYTHON: {e}\n\nLLM Response:\n{output}")
        return None


def parse_html_output(output: str) -> Optional[str]:
    """
    Parses the HTML output from the LLM.
    """
    output = output.strip()

    if output.lower() == "none":
        return None

    delimiters = [("```html\n", "\n```"), ("```\n", "\n```"), ("```", "```")]

    for delimiter in delimiters:
        if output.startswith(delimiter[0]):
            output = output[len(delimiter[0]) :]
        if output.endswith(delimiter[1]):
            output = output[: -len(delimiter[1])]
    return output


def parse_markdown_output(output: str) -> Optional[str]:
    """
    Parses the HTML output from the LLM.
    """
    output = output.strip()

    if output.lower() == "none":
        return None

    delimiters = [("```md\n", "\n```"), ("```\n", "\n```"), ("```", "```")]

    for delimiter in delimiters:
        if output.startswith(delimiter[0]):
            output = output[len(delimiter[0]) :]
        if output.endswith(delimiter[1]):
            output = output[: -len(delimiter[1])]
    return output


RETRY_JSON_PROMPT = """
Convert this to correct JSON:
```
{failed_json_str}
```
Respond with the correct JSON without ay other commentary
"""


async def retry_json_parse(
    failed_json_str: str,
    prompt: str = RETRY_JSON_PROMPT,
) -> str:
    orchestrator = LLMOrchestrator(DEFAULT_LLM_PROVIDER)
    formatted_prompt = prompt.format(failed_json_str=failed_json_str)
    response = await orchestrator.call_llm(formatted_prompt)
    return response


RETRY_PYTHON_PROMPT = """
Convert this to correct Python code:
```
{failed_python_str}
```
Respond only with the definition of the function, without example usage or other commentary.
"""


async def retry_python_parse(
    failed_python_str: str,
    prompt: str = RETRY_PYTHON_PROMPT,
) -> str:
    orchestrator = LLMOrchestrator(DEFAULT_LLM_PROVIDER)
    formatted_prompt = prompt.format(failed_python_str=failed_python_str)
    response = await orchestrator.call_llm(formatted_prompt)
    return response


import asyncio


async def test_llm_calls():
    # A simple prompt to verify which model is responding.
    orchestrator = LLMOrchestrator(DEFAULT_LLM_PROVIDER)
    prompt = "Are you a AI?"

    # Test call for GPT-4O-MINI deployment
    print("Testing GPT-4O-MINI deployment...")
    response_gpt4o_mini = await orchestrator.call_llm(prompt, max_tokens=150)
    print("Response from GPT-4O-MINI:")
    print(response_gpt4o_mini)


if __name__ == "__main__":
    asyncio.run(test_llm_calls())
