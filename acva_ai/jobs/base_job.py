"""
Base Job Classes for RQ Task Management

This module provides a class-based approach to RQ jobs, similar to Celery,
eliminating hardcoded function paths and making refactoring safer.
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import Any, Dict, Optional, Type

from rq import Queue, Retry

from acva_ai.models.job_metadata import BaseJobMetadata, JobMetadata, RetryInfo

logger = logging.getLogger(__name__)


class BaseJob(ABC):
    """
    Base class for all RQ jobs.

    This provides a consistent interface and eliminates the need for
    hardcoded function paths in job enqueuing.
    """

    # Job configuration (to be overridden in subclasses)
    name: str = "BaseJob"
    description: str = "Base job class"
    timeout: int = 1800  # 30 minutes default
    queue_name: str = "default"
    max_retries: int = 3
    retry_delay: int = 60  # seconds

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.start_time: datetime
        self.job_id: str
        self.metadata: Optional[JobMetadata] = None

    @abstractmethod
    def execute(self, *args, **kwargs) -> Any:
        """
        Execute the job logic. Must be implemented by subclasses.

        Args:
            *args: Positional arguments for the job
            **kwargs: Keyword arguments for the job

        Returns:
            Any: Job result
        """
        pass

    def get_retry_config(self) -> Optional[Retry]:
        """
        Get retry configuration for this job. Override in subclasses for custom retry logic.

        Returns:
            Retry configuration or None if no retries should be attempted
        """
        if self.max_retries <= 0:
            return None

        intervals = []
        for i in range(self.max_retries):
            delay = self.retry_delay * (i + 1)
            intervals.append(delay)

        return Retry(max=self.max_retries, interval=intervals)

    def is_retryable_error(self, exception: Exception) -> bool:
        """
        Determine if an exception should trigger a retry.
        Override in subclasses for custom retry logic.

        Args:
            exception: The exception that occurred

        Returns:
            True if the job should be retried, False otherwise
        """
        error_msg = str(exception).lower()

        retryable_patterns = [
            "timeout",
            "connection",
            "network",
            "temporary",
            "rate limit",
            "503",
            "502",
            "504",
        ]

        non_retryable_patterns = [
            "authentication",
            "authorization",
            "permission",
            "404",
            "400",
            "401",
            "403",
        ]

        for pattern in non_retryable_patterns:
            if pattern in error_msg:
                return False

        for pattern in retryable_patterns:
            if pattern in error_msg:
                return True

        exception_type = type(exception).__name__.lower()
        non_retryable_types = ["valueerror", "typeerror", "keyerror", "attributeerror"]

        return exception_type not in non_retryable_types

    def run(self, *args, **kwargs) -> Any:
        """
        Main entry point for RQ. Handles logging and error management.

        Args:
            *args: Positional arguments for the job
            **kwargs: Keyword arguments for the job

        Returns:
            Any: Job result
        """
        from rq import get_current_job

        self.start_time = datetime.now(timezone.utc)
        current_job = get_current_job()
        self.job_id = current_job.id if current_job else "unknown"
        self.current_job = current_job

        try:
            # Enhanced logging with metadata
            job_context = {
                "job_id": self.job_id,
                "job_name": self.name,
                "queue_name": self.queue_name,
                "args_count": len(args) if args else 0,
                "kwargs_keys": list(kwargs.keys()) if kwargs else [],
                "start_time": self.start_time.isoformat(),
            }

            # Initialize structured metadata from RQ job
            if current_job and hasattr(current_job, "meta") and current_job.meta:
                self.metadata = JobMetadata.from_dict(current_job.meta)
                job_context.update(self.metadata.job_metadata.to_dict())

            self.logger.info(
                f"Starting {self.name} (job_id: {self.job_id})",
                extra={"job_context": job_context},
            )

            # Update job progress to indicate start and add processing start time
            self._update_job_progress(0, "Starting job execution")
            self._add_processing_start_time()
            self._add_retry_info()

            # Execute the actual job logic
            result = self.execute(*args, **kwargs)

            duration = (datetime.now(timezone.utc) - self.start_time).total_seconds()

            # Final progress update with completion time
            self._update_job_progress(100, "Job completed successfully")
            self._add_processing_end_time(duration, "completed")

            completion_context = {
                **job_context,
                "duration_seconds": duration,
                "status": "completed",
                "end_time": datetime.now(timezone.utc).isoformat(),
            }

            self.logger.info(
                f"Completed {self.name} (job_id: {self.job_id}) in {duration:.2f}s",
                extra={"job_context": completion_context},
            )

            return result

        except Exception as e:
            duration = (datetime.now(timezone.utc) - self.start_time).total_seconds()

            # Update job progress to indicate failure
            self._update_job_progress(-1, f"Job failed: {str(e)}")
            self._add_processing_end_time(duration, "failed", str(e))

            error_context = {
                **job_context,
                "duration_seconds": duration,
                "status": "failed",
                "error_type": type(e).__name__,
                "error_message": str(e),
                "end_time": datetime.now(timezone.utc).isoformat(),
            }

            self.logger.error(
                f"Failed {self.name} (job_id: {self.job_id}) after {duration:.2f}s: {e}",
                extra={"job_context": error_context},
            )
            raise

    def _update_job_progress(self, progress: int, message: str) -> None:
        """
        Update job progress in RQ metadata for dashboard visibility.

        Args:
            progress: Progress percentage (0-100, or -1 for error)
            message: Status message describing current progress
        """
        try:
            if self.current_job and self.metadata:
                # Update structured metadata
                self.metadata.execution_metadata.progress_percentage = progress
                self.metadata.execution_metadata.progress_message = message
                self.current_job.meta = self.metadata.to_dict()
                self.current_job.save_meta()

                self.logger.debug(f"Updated job progress: {progress}% - {message}")
        except Exception as e:
            # Don't fail the job if progress update fails
            self.logger.warning(f"Failed to update job progress: {e}")

    def _add_processing_start_time(self) -> None:
        """
        Add processing start time to job metadata for enhanced tracking.
        This complements RQ's built-in started_at timestamp.
        """
        try:
            if self.current_job and self.metadata:
                # Update structured metadata
                self.metadata.execution_metadata.execution_started_at = self.start_time
                self.current_job.meta = self.metadata.to_dict()
                self.current_job.save_meta()

                self.logger.debug(
                    f"Added processing start time to job metadata: {self.start_time.isoformat()}"
                )
        except Exception as e:
            # Don't fail the job if metadata update fails
            self.logger.warning(f"Failed to add processing start time: {e}")

    def _add_retry_info(self) -> None:
        """
        Add retry debugging information to job metadata for better visibility.
        This includes RQ's internal retry state for debugging purposes.
        """
        try:
            if self.current_job and self.metadata:
                # Extract retry information from RQ job
                retry_info = self._get_retry_info()
                if retry_info:
                    self.metadata.execution_metadata.retry_info = RetryInfo(
                        **retry_info
                    )
                    self.current_job.meta = self.metadata.to_dict()
                    self.current_job.save_meta()

                    self.logger.debug(
                        f"Added retry debug info to job metadata: {retry_info}"
                    )
        except Exception as e:
            # Don't fail the job if metadata update fails
            self.logger.warning(f"Failed to add retry debug info: {e}")

    def _get_retry_info(self) -> Optional[dict]:
        """
        Extract retry debugging information from RQ job.

        Returns:
            Dictionary with retry information or None if not available
        """
        try:
            if not self.current_job:
                return None

            retry_info = {}

            # Get retry information from RQ job attributes
            if hasattr(self.current_job, "retries_left"):
                retry_info["retries_left"] = getattr(
                    self.current_job, "retries_left", None
                )

            if hasattr(self.current_job, "retry_intervals"):
                retry_info["retry_intervals"] = getattr(
                    self.current_job, "retry_intervals", None
                )

            # Add job status and timing info
            retry_info["job_status"] = getattr(
                self.current_job, "get_status", lambda: "unknown"
            )()

            # Add timestamps for retry tracking
            if hasattr(self.current_job, "started_at") and self.current_job.started_at:
                retry_info["started_at"] = self.current_job.started_at.isoformat()
            if hasattr(self.current_job, "created_at") and self.current_job.created_at:
                retry_info["created_at"] = self.current_job.created_at.isoformat()
            if (
                hasattr(self.current_job, "enqueued_at")
                and self.current_job.enqueued_at
            ):
                retry_info["enqueued_at"] = self.current_job.enqueued_at.isoformat()

            return retry_info if retry_info else None

        except Exception as e:
            self.logger.warning(f"Failed to extract retry debug info: {e}")
            return None

    def _add_processing_end_time(
        self, duration: float, status: str, error_message: Optional[str] = None
    ) -> None:
        """
        Add processing end time and final status to job metadata with execution attempt versioning.

        Args:
            duration: Processing duration in seconds
            status: Final status ("completed" or "failed")
            error_message: Error message if failed
        """
        try:
            if self.current_job and self.metadata:
                end_time = datetime.now(timezone.utc)

                # Update structured metadata
                self.metadata.execution_metadata.execution_ended_at = end_time
                self.metadata.execution_metadata.execution_duration = duration
                self.metadata.execution_metadata.status = status

                self.current_job.meta = self.metadata.to_dict()
                self.current_job.save_meta()

                self.logger.debug(
                    f"Added processing end time to job metadata: {end_time.isoformat()} (duration: {duration:.2f}s)"
                )
        except Exception as e:
            # Don't fail the job if metadata update fails
            self.logger.warning(f"Failed to add processing end time: {e}")

    @classmethod
    def get_job_function_path(cls):
        """
        Get the importable path for this job's RQ function.

        Returns:
            str: Module path to the job function
        """
        return f"{cls.__module__}.{cls.__name__}_rq_job"

    @classmethod
    def enqueue(cls, queue: Queue, *args, **kwargs):
        """
        Enqueue this job class with automatic retry configuration.

        Args:
            queue: The RQ queue instance
            *args: Arguments to pass to the job
            **kwargs: Keyword arguments (job args + RQ options)

        Returns:
            RQ Job instance
        """
        # Extract RQ-specific options from kwargs
        rq_options = {}
        job_kwargs = {}

        rq_option_keys = {
            "job_timeout",
            "result_ttl",
            "ttl",
            "failure_ttl",
            "depends_on",
            "job_id",
            "at",
            "in",
            "retry",
            "meta",
            "description",
        }

        for key, value in kwargs.items():
            if key in rq_option_keys:
                rq_options[key] = value
            else:
                job_kwargs[key] = value

        if "meta" in rq_options and isinstance(rq_options["meta"], BaseJobMetadata):
            # Convert structured metadata to dict
            metadata_obj = rq_options["meta"]
            combined_metadata = JobMetadata(job_metadata=metadata_obj)
            rq_options["meta"] = combined_metadata.to_dict()

        # Use class-specific timeout if not provided
        if "job_timeout" not in rq_options:
            rq_options["job_timeout"] = cls.timeout

        if "retry" not in rq_options:
            job_instance = cls()
            retry_config = job_instance.get_retry_config()
            if retry_config:
                rq_options["retry"] = retry_config

        logger.info(f"Enqueuing {cls.name} with args: {args[:2] if args else 'None'}")

        return queue.enqueue(
            cls.get_job_function_path(), *args, **job_kwargs, **rq_options
        )


class JobRegistry:
    """Registry for managing job classes."""

    def __init__(self):
        self._job_classes: Dict[str, Type[BaseJob]] = {}

    def register(self, job_class: Type[BaseJob]) -> Type[BaseJob]:
        """
        Register a job class.

        Args:
            job_class: The job class to register

        Returns:
            The job class (for use as decorator)
        """
        job_name = job_class.__name__
        if job_name in self._job_classes:
            logger.warning(f"Job class {job_name} is already registered, overwriting")

        self._job_classes[job_name] = job_class
        logger.info(f"Registered job class: {job_name}")

        return job_class

    def get_job_class(self, job_name: str) -> Type[BaseJob]:
        """
        Get a job class by name.

        Args:
            job_name: The job class name

        Returns:
            The job class

        Raises:
            KeyError: If job class is not registered
        """
        if job_name not in self._job_classes:
            raise KeyError(f"Job class {job_name} is not registered")
        return self._job_classes[job_name]

    def list_jobs(self) -> Dict[str, Type[BaseJob]]:
        """
        List all registered job classes.

        Returns:
            Dict mapping job names to job classes
        """
        return self._job_classes.copy()

    def validate_registry(self) -> bool:
        """
        Validate all registered job classes.

        Returns:
            bool: True if all jobs are valid
        """
        try:
            for job_name, job_class in self._job_classes.items():
                # Check that it's a proper subclass
                if not issubclass(job_class, BaseJob):
                    logger.error(f"Job {job_name} is not a BaseJob subclass")
                    return False

                # Check that it has proper configuration
                if not hasattr(job_class, "name") or not job_class.name:
                    logger.error(f"Job {job_name} has no name configured")
                    return False

                # Check timeout is reasonable
                if job_class.timeout <= 0 or job_class.timeout > 86400:
                    logger.error(
                        f"Job {job_name} has invalid timeout: {job_class.timeout}"
                    )
                    return False

                # Validate that execute method is implemented
                if (
                    not hasattr(job_class, "execute")
                    or job_class.execute is BaseJob.execute
                ):
                    logger.error(f"Job {job_name} has not implemented execute method")
                    return False

            logger.info(
                f"Job registry validation passed for {len(self._job_classes)} job classes"
            )
            return True

        except Exception as e:
            logger.error(f"Job registry validation failed: {e}")
            return False


# Global job registry instance
job_registry = JobRegistry()


def register_job(job_class: Type[BaseJob]) -> Type[BaseJob]:
    """
    Decorator to register a job class.

    Args:
        job_class: The job class to register

    Returns:
        The job class
    """
    return job_registry.register(job_class)
