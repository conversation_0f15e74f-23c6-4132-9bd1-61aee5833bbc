import logging
import traceback
from typing import List, Optional

from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.scenarios.medication import extract_medication
from acva_ai.models.medication import Medication
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.services.search import Search
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def _process_medication(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
) -> List[Medication]:
    meds = await extract_medication(
        transcript=transcript,
        llm_orchestrator=llm_orchestrator,
        response_usage=response_usage,
    )

    search_engine = Search()

    medication_list = []
    for medication_name, medication_context, rag_medication_name in meds:
        prospect = reference_url = None
        # if medication_name:
        #     prospect, reference_url = await search_engine.run_search(medication_name)
        if medication_name or medication_context:
            medication = Medication(
                medication_name=medication_name,
                medication_context=medication_context,
                prospect=prospect,
                reference_url=reference_url,  # Placeholder for future URL reference
                extracted_rag_name=rag_medication_name,
            )
            medication_list.append(medication)

    return medication_list


async def process_medication(
    task_id: str,
    processing_status: ProcessingStatus,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    response_usage: ResponseUsage = None,
):
    try:
        logger.info(f"[Task {task_id}] Starting medication verification")
        processing_status.start_stage("medication_verification")
        mongo_instance.update_processing_status(task_id, processing_status.dict())

        transcript = (
            visit_report.transcript
            if visit_report.transcript
            else visit_report.raw_transcript
        )

        medications = await _process_medication(
            transcript,
            llm_orchestrator=llm_orchestrator,
            response_usage=response_usage,
        )
        visit_report.medications = medications
        processing_status.complete_stage("medication_verification", medications)
        logger.info(f"[Task {task_id}] Completed medication verification")
    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("medication_verification", e, stack_trace)
        logger.error(
            f"[Task {task_id}] Error verifying medications: {e}\n{stack_trace}"
        )
    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
