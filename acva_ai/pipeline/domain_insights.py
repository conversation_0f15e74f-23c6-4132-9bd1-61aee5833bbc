import asyncio
import logging
import traceback
from typing import Optional

from acva_ai.database import mongo_instance
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.llm.scenarios.domain_insight import extract_insight
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.services.acva_server import retrieve_domain_insights
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


async def _process_domain_insights(
    task_id: str,
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    visit_report: VisitReport,
    response_usage: Optional[ResponseUsage] = None,
):
    domain_insights = retrieve_domain_insights(task_id)
    if not len(domain_insights):
        raise ValueError("No Domain Insights retrieved for Task {task_id}")

    tasks = []
    for domain_insight in domain_insights:

        tasks.append(
            extract_insight(
                transcript=transcript,
                insight_name=domain_insight.insight_name,
                insight_extraction_prompt=domain_insight.insight_extraction_prompt,
                llm_orchestrator=llm_orchestrator,
                response_usage=response_usage,
                visit_report=visit_report,
            )
        )

    results = await asyncio.gather(*tasks)
    for i, domain_insight in enumerate(domain_insights):
        domain_insight.insight_content = results[i]

    return domain_insights


async def process_domain_insights(
    task_id: str,
    processing_status: ProcessingStatus,
    visit_report: VisitReport,
    llm_orchestrator: LLMOrchestrator,
    response_usage: Optional[ResponseUsage] = None,
):
    try:
        processing_status.start_stage("domain_insights")
        mongo_instance.update_processing_status(task_id, processing_status.dict())

        logger.info(f"[Task {task_id}] Starting domain insights audio_processing")
        # Use labeled speaker data if available, otherwise fall back to transcript/raw_transcript
        if visit_report.speakers_data:
            # Serialize the labeled speaker data into a readable format
            transcript_parts = []
            for item in visit_report.speakers_data:
                for speaker, sentence in item.items():
                    transcript_parts.append(f"{speaker}: {sentence}")
            transcript = "\n".join(transcript_parts)
        else:
            transcript = (
                visit_report.transcript
                if visit_report.transcript
                else visit_report.raw_transcript
            )

        if not transcript:
            raise ValueError(f"No transcript found for task {task_id}")

        domain_insights = await _process_domain_insights(
            task_id=task_id,
            transcript=transcript,
            llm_orchestrator=llm_orchestrator,
            response_usage=response_usage,
            visit_report=visit_report,
        )
        visit_report.domain_insights = domain_insights
        processing_status.complete_stage("domain_insights")
        logger.info(f"[Task {task_id}] Completed domain insights audio_processing")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("domain_insights", e, stack_trace)
        logger.error(
            f"[Task {task_id}] Error audio_processing domain insights: {e}\n{stack_trace}"
        )
    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())


def test():
    transcript = """Colecist, 6 cm, locul drept, 14 cm, aspect omogen, hiperecogen cu atenuare posterioră, fără dilatații, venă portă normală.Așa că vorbim de hepatopatie.Da.Ok.Trebuie să specific corect.```\nAm înțeles, gata, organul, orto.\n``````\nPereți normal, conținut transsonic, cu prezența unei imagini hiperecogene, cu diametru bine delimitată, cu diametru de 2,2 cm, cu fond de umbră posterioră.\n```Splina, diametru anteroposterior de 14 cm, omogenă normoecogenă, linic drept, normal situat, contur normal, la nivelul medulare, prezența unor imagini uniforme hiperecogene, fără fond de umbră posterior, fără dilatație ale sistemului pielocalicial.```\nLinicul stâng, poziție normală, contur normal, cu prezența la nivelul medulare unei imagini transsonice bine delimitată.\n```Conținut transsonic de 3,3 cm, fără retenție de urină, în vezica urinară.```\nVezică urinară semirepleție, conținut transsonic, fără formațiuni protruizive.\n``````\nGroscată omogenă, vascularizație normală, cu prezența unei imagini mai hiperecogene, situată central, cu diametru de 2,3 cm.\n``````\nStaatoză hepatică gradul 2, litiază veziculară, nefrolitiază, chist renal stâng.\n```"""
    # TODO Implement an actual test


if __name__ == "__main__":
    test()
