import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import PyMongoError

from acva_ai._params import MONGO_DB_CONNECTION_STRING
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport

logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder for datetime and UUID objects"""

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, UUID):
            return str(obj)
        return super().default(obj)


class MongoDB:
    def __init__(self):
        self.connection_string = str(MONGO_DB_CONNECTION_STRING)
        self.client: MongoClient
        self.database: Database
        self.processing_status: Collection
        self.visit_reports: Collection
        self.tasks: Collection
        self.audio_tasks: Collection
        self.medical_reports: Collection

        self._initialized = False

    def initialize(self):
        """Initialize database and collections if not already initialized"""
        if self._initialized:
            logger.debug("Database already initialized, skipping initialization")
            return

        try:
            # Connect to MongoDB
            self.client = MongoClient(self.connection_string)

            # Test connection
            self.client.admin.command("ping")
            logger.info("Successfully connected to MongoDB")

            # Get or create database
            self.database = self.client.get_database("acva-medical")
            logger.info(f"Using database: acva-medical")

            # Initialize audio_processing status collection
            self.processing_status = self.database.get_collection(
                "audio_processing-status"
            )
            self._ensure_processing_status_indexes()
            logger.info("Initialized audio_processing-status collection")

            # Initialize visit reports collection
            self.visit_reports = self.database.get_collection("visit-reports")
            self._ensure_visit_reports_indexes()
            logger.info("Initialized visit-reports collection")

            # Initialize tasks collection
            self.tasks = self.database.get_collection("tasks")
            self._ensure_tasks_indexes()
            logger.info("Initialized tasks collection")

            # Initialize audio tasks collection
            self.audio_tasks = self.database.get_collection("audio_tasks")
            self._ensure_audio_tasks_indexes()
            logger.info("Initialized audio_tasks collection")

            # Initialize medical reports collection
            self.medical_reports = self.database.get_collection("medical_reports")
            self._ensure_medical_reports_indexes()
            logger.info("Initialized medical_reports collection")

            # Initialize visit jobs and batches collections
            self.visit_jobs = self.database.get_collection("visit_jobs")
            self._ensure_visit_jobs_indexes()
            logger.info("Initialized visit_jobs collection")

            self.batches = self.database.get_collection("batches")
            self._ensure_batches_indexes()
            logger.info("Initialized batches collection")

            self._initialized = True
            logger.info("Successfully initialized MongoDB connection")

        except PyMongoError as e:
            logger.error(f"Failed to initialize MongoDB: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error initializing MongoDB: {str(e)}")
            raise

    def _ensure_processing_status_indexes(self):
        """Create indexes for audio_processing status collection"""
        try:
            # Create unique index on task_id
            self.processing_status.create_index("task_id", unique=True)

            # Create index on overall_status for filtering
            self.processing_status.create_index("overall_status")

            # Create compound index for sorting by status and timestamp
            self.processing_status.create_index(
                [("overall_status", 1), ("start_time", -1)]
            )

            logger.debug("Processing status indexes created/verified")
        except Exception as e:
            logger.warning(
                f"Failed to create audio_processing status indexes: {str(e)}"
            )

    def _ensure_visit_reports_indexes(self):
        """Create indexes for visit reports collection"""
        try:
            # Create unique index on task_id
            self.visit_reports.create_index("task_id", unique=True)

            # Create index on created_at for sorting
            self.visit_reports.create_index([("created_at", -1)])

            logger.debug("Visit reports indexes created/verified")
        except Exception as e:
            logger.warning(f"Failed to create visit reports indexes: {str(e)}")

    def _ensure_tasks_indexes(self):
        """Create indexes for tasks collection"""
        try:
            # Create unique index on task_id
            self.tasks.create_index("task_id", unique=True)

            # Create index on status for filtering
            self.tasks.create_index("status")

            # Create index on created_at for sorting
            self.tasks.create_index([("created_at", -1)])

            logger.debug("Tasks indexes created/verified")
        except Exception as e:
            logger.warning(f"Failed to create tasks indexes: {str(e)}")

    def _ensure_audio_tasks_indexes(self):
        """Create indexes for audio tasks collection"""
        try:
            # Create unique index on task_id
            self.audio_tasks.create_index("task_id", unique=True)

            # Create index on stream_id for lookup
            self.audio_tasks.create_index("stream_id")

            logger.debug("Audio tasks indexes created/verified")
        except Exception as e:
            logger.warning(f"Failed to create audio tasks indexes: {str(e)}")

    def _ensure_medical_reports_indexes(self):
        """Create indexes for medical reports collection"""
        try:
            # Create unique index on task_id
            self.medical_reports.create_index("task_id", unique=True)

            logger.debug("Medical reports indexes created/verified")
        except Exception as e:
            logger.warning(f"Failed to create medical reports indexes: {str(e)}")

    def _ensure_visit_jobs_indexes(self):
        """Create indexes for visit jobs collection"""
        try:
            # Create unique index on visit_job_id
            self.visit_jobs.create_index("visit_job_id", unique=True)

            # Create index on status for filtering
            self.visit_jobs.create_index("status")

            # Create index on created_at for sorting
            self.visit_jobs.create_index([("created_at", -1)])

            # Create index on finish_signal_received for queries
            self.visit_jobs.create_index("finish_signal_received")

            # Create compound index for status and created_at
            self.visit_jobs.create_index([("status", 1), ("created_at", -1)])

            logger.debug("Visit jobs indexes created/verified")
        except Exception as e:
            logger.warning(f"Failed to create visit jobs indexes: {str(e)}")

    def _ensure_batches_indexes(self):
        """Create indexes for batches collection"""
        try:
            # Create unique index on batch_id
            self.batches.create_index("batch_id", unique=True)

            # Create index on visit_job_id for lookup
            self.batches.create_index("visit_job_id")

            # Create index on status for filtering
            self.batches.create_index("status")

            # Create index on batch_number for ordering
            self.batches.create_index("batch_number")

            # Create compound index for visit_job_id + batch_number
            self.batches.create_index([("visit_job_id", 1), ("batch_number", 1)])

            logger.debug("Batches indexes created/verified")
        except Exception as e:
            logger.warning(f"Failed to create batches indexes: {str(e)}")

    def _serialize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize data to JSON-compatible format"""
        return json.loads(json.dumps(data, cls=DateTimeEncoder))

    def update_processing_status(
        self, task_id: str, status_data: Dict[str, Any]
    ) -> None:
        """Update audio_processing status document"""
        if not self._initialized:
            self.initialize()

        try:
            # Convert to ProcessingStatus if dict is provided
            if isinstance(status_data, dict):
                status = ProcessingStatus(**status_data)
                status_data = status.dict()

            # Serialize data for MongoDB
            serialized_data = self._serialize_data(status_data)

            # Use upsert operation
            self.processing_status.update_one(
                {"task_id": task_id},
                {"$set": {"task_id": task_id, **serialized_data}},
                upsert=True,
            )
            logger.debug(f"Updated audio_processing status for task {task_id}")
        except PyMongoError as e:
            logger.error(
                f"Failed to update audio_processing status for task {task_id}: {str(e)}"
            )
            raise

    def save_visit_report(self, task_id: str, report_data: Dict[str, Any]) -> None:
        """Save or update the visit report"""
        if not self._initialized:
            self.initialize()

        try:
            # Convert to VisitReport if dict is provided
            if isinstance(report_data, dict):
                report = VisitReport(**report_data)
                report_data = report.dict()

            # Serialize data for MongoDB
            serialized_data = self._serialize_data(report_data)

            # Use upsert operation
            self.visit_reports.update_one(
                {"task_id": task_id},
                {"$set": {"task_id": task_id, **serialized_data}},
                upsert=True,
            )
            logger.debug(f"Saved visit report for task {task_id}")
        except PyMongoError as e:
            logger.error(f"Failed to save visit report for task {task_id}: {str(e)}")
            raise

    def get_processing_status(self, task_id: str) -> Optional[ProcessingStatus]:
        """Get current audio_processing status"""
        if not self._initialized:
            self.initialize()

        try:
            status_data = self.processing_status.find_one({"task_id": task_id})
            if status_data:
                # Remove MongoDB's _id field
                status_data.pop("_id", None)
                return ProcessingStatus(**status_data)
            return None
        except PyMongoError as e:
            logger.error(
                f"Failed to get audio_processing status for task {task_id}: {str(e)}"
            )
            raise

    def get_visit_report(self, task_id: str) -> Optional[VisitReport]:
        """Get visit report"""
        if not self._initialized:
            self.initialize()

        try:
            report_data = self.visit_reports.find_one({"task_id": task_id})
            if report_data:
                # Remove MongoDB's _id field
                report_data.pop("_id", None)
                return VisitReport(**report_data)
            return None
        except PyMongoError as e:
            logger.error(f"Failed to get visit report for task {task_id}: {str(e)}")
            raise

    def list_processing_statuses(
        self,
        status: Optional[str] = None,
        limit: int = 100,
        continuation_token: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List audio_processing statuses with optional filtering"""
        if not self._initialized:
            self.initialize()

        try:
            # Build query filter
            query_filter = {}
            if status:
                query_filter["overall_status"] = status

            # Build sort criteria
            sort_criteria = [("start_time", -1)]  # Order by timestamp -1

            # Execute query
            cursor = (
                self.processing_status.find(query_filter)
                .sort(sort_criteria)
                .limit(limit)
            )

            # Convert cursor to list and remove _id fields
            items = []
            for item in cursor:
                item.pop("_id", None)
                items.append(item)

            # Convert items to ProcessingStatus objects
            status_items = []
            for item in items:
                try:
                    status_items.append(ProcessingStatus(**item))
                except Exception as e:
                    logger.warning(f"Failed to parse status item: {str(e)}")
                    continue

            # MongoDB doesn't have continuation tokens like Cosmos DB
            # We'll return None for continuation_token to maintain interface compatibility
            return {"items": status_items, "continuation_token": None}

        except PyMongoError as e:
            logger.error(f"Failed to list audio_processing statuses: {str(e)}")
            raise

    # Additional methods for compatibility with existing codebase

    def create_task(self, task_data: Dict[str, Any]) -> None:
        """Create a new task"""
        if not self._initialized:
            self.initialize()

        try:
            # Serialize data for MongoDB
            serialized_data = self._serialize_data(task_data)

            # Insert new task
            self.tasks.insert_one(serialized_data)
            logger.debug(f"Created task: {task_data.get('task_id', 'unknown')}")
        except PyMongoError as e:
            logger.error(f"Failed to create task: {str(e)}")
            raise

    def update_task_status(
        self, task_id: str, status: str, error_message: Optional[str] = None
    ) -> None:
        """Update task status"""
        if not self._initialized:
            self.initialize()

        try:
            update_data = {"status": status}
            if error_message:
                update_data["error_message"] = error_message

            self.tasks.update_one({"task_id": task_id}, {"$set": update_data})
            logger.debug(f"Updated task {task_id} status to {status}")
        except PyMongoError as e:
            logger.error(f"Failed to update task status for {task_id}: {str(e)}")
            raise

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task by ID"""
        if not self._initialized:
            self.initialize()

        try:
            task_data = self.tasks.find_one({"task_id": task_id})
            if task_data:
                # Remove MongoDB's _id field
                task_data.pop("_id", None)
                return task_data
            return None
        except PyMongoError as e:
            logger.error(f"Failed to get task {task_id}: {str(e)}")
            raise

    def get_tasks(self) -> List[Dict[str, Any]]:
        """Get all tasks"""
        if not self._initialized:
            self.initialize()

        try:
            cursor = self.tasks.find().sort("created_at", -1)
            tasks = []
            for task in cursor:
                task.pop("_id", None)
                tasks.append(task)
            return tasks
        except PyMongoError as e:
            logger.error(f"Failed to get tasks: {str(e)}")
            raise

    def insert_audio_task(self, audio_task_data: Dict[str, Any]) -> None:
        """Insert audio task"""
        if not self._initialized:
            self.initialize()

        try:
            # Serialize data for MongoDB
            serialized_data = self._serialize_data(audio_task_data)

            # Insert audio task
            self.audio_tasks.insert_one(serialized_data)
            logger.debug(
                f"Inserted audio task: {audio_task_data.get('task_id', 'unknown')}"
            )
        except PyMongoError as e:
            logger.error(f"Failed to insert audio task: {str(e)}")
            raise

    def update_cost_and_time(self, task_id: str, cost: float) -> None:
        """Update cost and time for a task"""
        if not self._initialized:
            self.initialize()

        try:
            self.tasks.update_one(
                {"task_id": task_id},
                {"$set": {"cost": cost, "updated_at": datetime.utcnow().isoformat()}},
            )
            logger.debug(f"Updated cost for task {task_id}: {cost}")
        except PyMongoError as e:
            logger.error(f"Failed to update cost for task {task_id}: {str(e)}")
            raise

    def get_medical_report(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get medical report by task ID"""
        if not self._initialized:
            self.initialize()

        try:
            report_data = self.medical_reports.find_one({"task_id": task_id})
            if report_data:
                # Remove MongoDB's _id field
                report_data.pop("_id", None)
                return report_data
            return None
        except PyMongoError as e:
            logger.error(f"Failed to get medical report for task {task_id}: {str(e)}")
            raise

    # Visit Jobs CRUD Operations
    def create_visit_job(self, job_data: Dict[str, Any]) -> None:
        """Create a new visit job"""
        if not self._initialized:
            self.initialize()

        try:
            serialized_data = self._serialize_data(job_data)
            self.visit_jobs.insert_one(serialized_data)
            logger.debug(f"Created visit job {job_data.get('visit_job_id')}")
        except PyMongoError as e:
            logger.error(f"Failed to create visit job: {str(e)}")
            raise

    def update_visit_job(self, visit_job_id: str, update_data: Dict[str, Any]) -> None:
        """Update a visit job"""
        if not self._initialized:
            self.initialize()

        try:
            serialized_data = self._serialize_data(update_data)
            result = self.visit_jobs.update_one(
                {"visit_job_id": visit_job_id}, {"$set": serialized_data}
            )
            if result.matched_count == 0:
                logger.warning(f"Visit job {visit_job_id} not found for update")
            else:
                logger.debug(f"Updated visit job {visit_job_id}")
        except PyMongoError as e:
            logger.error(f"Failed to update visit job {visit_job_id}: {str(e)}")
            raise

    def get_visit_job(self, visit_job_id: str) -> Optional[Dict[str, Any]]:
        """Get a visit job by ID"""
        if not self._initialized:
            self.initialize()

        try:
            result = self.visit_jobs.find_one({"visit_job_id": visit_job_id})
            if result:
                # Remove MongoDB's _id field
                result.pop("_id", None)
                logger.debug(f"Retrieved visit job {visit_job_id}")
            return result
        except PyMongoError as e:
            logger.error(f"Failed to get visit job {visit_job_id}: {str(e)}")
            raise

    def get_visit_jobs(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all visit jobs, optionally filtered by status"""
        if not self._initialized:
            self.initialize()

        try:
            query = {}
            if status:
                query["status"] = status

            results = list(self.visit_jobs.find(query).sort("created_at", -1))
            # Remove MongoDB's _id field from all results
            for result in results:
                result.pop("_id", None)

            logger.debug(f"Retrieved {len(results)} visit jobs")
            return results
        except PyMongoError as e:
            logger.error(f"Failed to get visit jobs: {str(e)}")
            raise

    # Batches CRUD Operations
    def create_batch(self, batch_data: Dict[str, Any]) -> None:
        """Create a new batch"""
        if not self._initialized:
            self.initialize()

        try:
            serialized_data = self._serialize_data(batch_data)
            self.batches.insert_one(serialized_data)
            logger.debug(f"Created batch {batch_data.get('batch_id')}")
        except PyMongoError as e:
            logger.error(f"Failed to create batch: {str(e)}")
            raise

    def update_batch(self, batch_id: str, update_data: Dict[str, Any]) -> None:
        """Update a batch"""
        if not self._initialized:
            self.initialize()

        try:
            serialized_data = self._serialize_data(update_data)
            result = self.batches.update_one(
                {"batch_id": batch_id}, {"$set": serialized_data}
            )
            if result.matched_count == 0:
                logger.warning(f"Batch {batch_id} not found for update")
            else:
                logger.debug(f"Updated batch {batch_id}")
        except PyMongoError as e:
            logger.error(f"Failed to update batch {batch_id}: {str(e)}")
            raise

    def get_batch(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """Get a batch by ID"""
        if not self._initialized:
            self.initialize()

        try:
            result = self.batches.find_one({"batch_id": batch_id})
            if result:
                # Remove MongoDB's _id field
                result.pop("_id", None)
                logger.debug(f"Retrieved batch {batch_id}")
            return result
        except PyMongoError as e:
            logger.error(f"Failed to get batch {batch_id}: {str(e)}")
            raise

    def get_batches_by_visit_job(self, visit_job_id: str) -> List[Dict[str, Any]]:
        """Get all batches for a visit job"""
        if not self._initialized:
            self.initialize()

        try:
            results = list(
                self.batches.find({"visit_job_id": visit_job_id}).sort(
                    "batch_number", 1
                )
            )
            # Remove MongoDB's _id field from all results
            for result in results:
                result.pop("_id", None)

            logger.debug(
                f"Retrieved {len(results)} batches for visit job {visit_job_id}"
            )
            return results
        except PyMongoError as e:
            logger.error(
                f"Failed to get batches for visit job {visit_job_id}: {str(e)}"
            )
            raise

    def close(self):
        """Close the MongoDB connection"""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")


# Create singleton instance
mongo_instance = MongoDB()
