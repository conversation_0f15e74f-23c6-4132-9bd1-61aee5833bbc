import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from acva_ai.database.mongo import mongo_instance
from acva_ai.models.visit_job import Batch, BatchStatus

logger = logging.getLogger(__name__)


class VisitBatchOrderingService:
    """Service for managing batch ordering and dependencies within visit jobs."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def validate_batch_ordering(self, visit_job_id: str) -> Tuple[bool, Optional[str]]:
        """
        Validate that batches can be properly ordered within a visit job.

        Args:
            visit_job_id: The visit job ID

        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            if not batches:
                return False, "No batches found for visit job"

            # Convert to Batch objects for easier handling
            batch_objects = [Batch(**batch) for batch in batches]

            # Check for duplicate batch numbers
            batch_numbers = [batch.batch_number for batch in batch_objects]
            if len(batch_numbers) != len(set(batch_numbers)):
                duplicates = [
                    num for num in batch_numbers if batch_numbers.count(num) > 1
                ]
                return False, f"Duplicate batch numbers found: {set(duplicates)}"

            # Check for missing batch numbers (should be sequential from 1)
            expected_numbers = set(range(1, len(batch_objects) + 1))
            actual_numbers = set(batch_numbers)
            if expected_numbers != actual_numbers:
                missing = expected_numbers - actual_numbers
                extra = actual_numbers - expected_numbers
                error_msg = ""
                if missing:
                    error_msg += f"Missing batch numbers: {sorted(missing)}"
                if extra:
                    error_msg += f"Extra batch numbers: {sorted(extra)}"
                return False, error_msg

            return True, None

        except Exception as e:
            self.logger.error(
                f"Error validating batch ordering for visit job {visit_job_id}: {e}"
            )
            return False, f"Validation error: {e}"

    def get_processing_order(self, visit_job_id: str) -> List[Dict]:
        """
        Get the recommended processing order for batches within a visit job.

        Args:
            visit_job_id: The visit job ID

        Returns:
            List of batch dictionaries ordered by processing priority
        """
        try:
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            if not batches:
                return []

            # Convert to Batch objects
            batch_objects = [Batch(**batch) for batch in batches]

            # Sort by batch number (this is the definitive order)
            sorted_batches = sorted(batch_objects, key=lambda x: x.batch_number)

            # Convert back to dictionaries
            return [batch.model_dump() for batch in sorted_batches]

        except Exception as e:
            self.logger.error(
                f"Error getting processing order for visit job {visit_job_id}: {e}"
            )
            return []

    def check_batch_dependencies(
        self, visit_job_id: str, batch_number: int
    ) -> Tuple[bool, Optional[str]]:
        """
        Check if a batch can be processed based on its dependencies.

        Args:
            visit_job_id: The visit job ID
            batch_number: The batch number to check

        Returns:
            Tuple of (can_process, reason)
        """
        try:
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            if not batches:
                return False, "No batches found for visit job"

            # For visit jobs, batches can be processed independently in parallel
            # The final assembly step will handle proper ordering

            # Check if this batch exists and is in the right state
            current_batch = None
            for batch in batches:
                if batch.get("batch_number") == batch_number:
                    current_batch = batch
                    break

            if not current_batch:
                return False, f"Batch {batch_number} not found"

            status = current_batch.get("status")
            if status != BatchStatus.UPLOADED.value:
                return (
                    False,
                    f"Batch {batch_number} is not in uploaded state (current: {status})",
                )

            return True, None

        except Exception as e:
            self.logger.error(
                f"Error checking batch dependencies for visit job {visit_job_id}, batch {batch_number}: {e}"
            )
            return False, f"Dependency check error: {e}"

    def get_assembly_order(self, visit_job_id: str) -> List[Dict]:
        """
        Get the order in which batches should be assembled for final processing.

        Args:
            visit_job_id: The visit job ID

        Returns:
            List of completed batch dictionaries in assembly order
        """
        try:
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            if not batches:
                return []

            # Filter to only completed batches
            completed_batches = [
                batch
                for batch in batches
                if batch.get("status") == BatchStatus.COMPLETED.value
            ]

            # Convert to Batch objects and sort by batch number
            batch_objects = [Batch(**batch) for batch in completed_batches]
            sorted_batches = sorted(batch_objects, key=lambda x: x.batch_number)

            # Convert back to dictionaries
            return [batch.model_dump() for batch in sorted_batches]

        except Exception as e:
            self.logger.error(
                f"Error getting assembly order for visit job {visit_job_id}: {e}"
            )
            return []

    def validate_assembly_readiness(
        self, visit_job_id: str
    ) -> Tuple[bool, Optional[str]]:
        """
        Validate that a visit job is ready for assembly.

        Args:
            visit_job_id: The visit job ID

        Returns:
            Tuple of (is_ready, reason)
        """
        try:
            # Get visit job data
            visit_job_data = mongo_instance.get_visit_job(visit_job_id)
            if not visit_job_data:
                return False, "Visit job not found"

            # Check if finish signal received
            if not visit_job_data.get("finish_signal_received", False):
                return False, "Finish signal not received"

            # Get all batches
            batches = mongo_instance.get_batches_by_visit_job(visit_job_id)
            if not batches:
                return False, "No batches found"

            # Check batch statuses
            completed_batches = [
                b for b in batches if b.get("status") == BatchStatus.COMPLETED.value
            ]
            failed_batches = [
                b for b in batches if b.get("status") == BatchStatus.FAILED.value
            ]

            # If any batch failed, cannot assemble
            if failed_batches:
                return False, f"{len(failed_batches)} batch(es) failed"

            # Check if all batches are completed
            if len(completed_batches) != len(batches):
                return False, "Not all batches are completed"

            # Validate batch ordering
            is_valid, error_msg = self.validate_batch_ordering(visit_job_id)
            if not is_valid:
                return False, f"Batch ordering invalid: {error_msg}"

            return True, None

        except Exception as e:
            self.logger.error(
                f"Error validating assembly readiness for visit job {visit_job_id}: {e}"
            )
            return False, f"Validation error: {e}"
