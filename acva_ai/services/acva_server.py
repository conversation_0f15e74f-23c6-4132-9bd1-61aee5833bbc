import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

import httpx

from acva_ai._params import ACVA_CALLBACK_URL, ACVA_REPORT_FIELDS, API_KEY
from acva_ai.models.domain_insight import DomainInsight
from acva_ai.models.visit_report import VisitReport

logger = logging.getLogger(__name__)


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle UUID and datetime objects."""

    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


def validate_webhook_url(url: str) -> tuple[bool, str]:
    """
    Validate webhook URL format and provide specific error messages.

    Args:
        url: The webhook URL to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not url:
        return False, "Webhook URL is empty or None"

    if not url.strip():
        return False, "Webhook URL is blank or contains only whitespace"

    # Check for protocol
    if not url.startswith(("http://", "https://")):
        return (
            False,
            f"Webhook URL '{url}' is missing protocol. URLs must start with 'http://' or 'https://'. Example: 'http://localhost:6000' instead of 'localhost:6000'",
        )

    # Parse URL to check validity
    try:
        parsed = urlparse(url)
        if not parsed.netloc:
            return (
                False,
                f"Webhook URL '{url}' has invalid format - missing hostname/domain",
            )
        if not parsed.scheme:
            return (
                False,
                f"Webhook URL '{url}' has invalid format - missing protocol scheme",
            )
    except Exception as e:
        return False, f"Webhook URL '{url}' is malformed: {str(e)}"

    return True, ""


class WebhookService:
    """Service for sending webhooks with retry logic and exponential backoff."""

    def __init__(self):
        self.max_retries = 5
        self.base_delay = 1.0  # Base delay in seconds
        self.max_delay = 300.0  # Maximum delay (5 minutes)
        self.timeout = 30.0  # Request timeout in seconds

    async def send_webhook_with_retry(
        self,
        webhook_url: str,
        payload: Dict[str, Any],
        headers: Optional[Dict[str, str]] = None,
        retry_count: int = 0,
    ) -> bool:
        """
        Send webhook with exponential backoff retry logic.

        Args:
            webhook_url: The webhook URL to send to
            payload: The payload to send
            headers: Optional headers to include
            retry_count: Current retry attempt (for recursion)

        Returns:
            bool: True if successful, False if all retries failed
        """
        if headers is None:
            headers = {"Content-Type": "application/json"}

        # Validate URL before attempting to send
        is_valid, error_message = validate_webhook_url(webhook_url)
        if not is_valid:
            logger.error(f"Invalid webhook URL configuration: {error_message}")
            return False

        # Convert payload to JSON string
        try:
            json_payload = json.dumps(payload, cls=CustomJSONEncoder)
        except Exception as e:
            logger.error(f"Failed to serialize webhook payload for {webhook_url}: {e}")
            return False

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    webhook_url, content=json_payload, headers=headers
                )

                # Consider 2xx status codes as success
                if 200 <= response.status_code < 300:
                    logger.info(
                        f"Webhook sent successfully to {webhook_url} (status: {response.status_code})"
                    )
                    return True

                # For 4xx errors (client errors), don't retry
                elif 400 <= response.status_code < 500:
                    logger.error(
                        f"Webhook failed with client error {response.status_code} to {webhook_url}. Response: {response.text if hasattr(response, 'text') else 'N/A'}. Not retrying."
                    )
                    return False

                # For 5xx errors (server errors), retry
                else:
                    logger.warning(
                        f"Webhook failed with server error {response.status_code} to {webhook_url}. Response: {response.text if hasattr(response, 'text') else 'N/A'}. Will retry."
                    )
                    return await self._retry_webhook(
                        webhook_url, payload, headers, retry_count
                    )

        except httpx.TimeoutException:
            logger.warning(
                f"Webhook timeout ({self.timeout}s) to {webhook_url}. Will retry."
            )
            return await self._retry_webhook(webhook_url, payload, headers, retry_count)
        except httpx.ConnectError as e:
            logger.warning(f"Connection error to {webhook_url}: {str(e)}. Will retry.")
            return await self._retry_webhook(webhook_url, payload, headers, retry_count)
        except httpx.InvalidURL as e:
            logger.error(
                f"Invalid URL format for webhook {webhook_url}: {str(e)}. This is a configuration error and will not be retried."
            )
            return False
        except Exception as e:
            logger.error(
                f"Unexpected error sending webhook to {webhook_url}: {str(e)} (Error type: {type(e).__name__})"
            )
            return await self._retry_webhook(webhook_url, payload, headers, retry_count)

    async def _retry_webhook(
        self,
        webhook_url: str,
        payload: Dict[str, Any],
        headers: Dict[str, str],
        retry_count: int,
    ) -> bool:
        """Handle webhook retry logic with exponential backoff."""
        if retry_count >= self.max_retries:
            logger.error(
                f"Webhook failed after {self.max_retries} attempts to {webhook_url}"
            )
            return False

        # Calculate exponential backoff delay
        delay = min(self.base_delay * (2**retry_count), self.max_delay)

        logger.info(
            f"Retrying webhook to {webhook_url} in {delay:.1f}s (attempt {retry_count + 1}/{self.max_retries})"
        )
        await asyncio.sleep(delay)

        return await self.send_webhook_with_retry(
            webhook_url, payload, headers, retry_count + 1
        )

    async def send_visit_job_webhook(
        self,
        webhook_url: str,
        visit_job_id: str,
        event_type: str,
        status: str,
        data: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Send a visit job webhook notification.

        Args:
            webhook_url: The webhook URL
            visit_job_id: The visit job ID
            event_type: Type of event (batch_completed, visit_completed, etc.)
            status: Current status
            data: Additional event data

        Returns:
            bool: True if successful
        """
        payload = {
            "event_type": event_type,
            "visit_job_id": visit_job_id,
            "status": status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": data or {},
        }

        headers = {
            "Content-Type": "application/json",
            "X-Event-Type": event_type,
            "X-Visit-Job-ID": visit_job_id,
        }

        return await self.send_webhook_with_retry(webhook_url, payload, headers)


# Global webhook service instance
webhook_service = WebhookService()


async def send_callback(task_id, visit_report: VisitReport):
    """
    Sends a callback with the medical report to the configured endpoint.

    Args:
        task_id: The unique identifier for the task.
        visit_report: The visit report to send.

    Returns:
        bool: True if the callback was successful, False otherwise.
    """
    try:
        # Get the report data using model_dump
        visit_report_data = visit_report.model_dump()

        # Prepare the payload
        payload = {
            "task_id": task_id,
            "medical_report": visit_report_data,
        }

        headers = {"X-API-Key": API_KEY, "Content-Type": "application/json"}

        if not ACVA_CALLBACK_URL:
            logger.error(f"[Task {task_id}] ACVA_CALLBACK_URL not configured")
            return False

        callback_url = ACVA_CALLBACK_URL.format(task_id=task_id)

        # Use the new webhook service with retry logic
        return await webhook_service.send_webhook_with_retry(
            webhook_url=callback_url, payload=payload, headers=headers
        )

    except Exception as e:
        logger.error(f"[Task {task_id}] Callback failed: {e}")
        return False


def retrieve_domain_insights(task_id: str) -> List[DomainInsight]:
    """
    Retrieve domain insights for a given task ID from the configured endpoint.

    Args:
        task_id: The unique identifier for the task.

    Returns:
        List of DomainInsight objects
    """
    if not ACVA_REPORT_FIELDS:
        logger.error("ACVA_REPORT_FIELDS not configured")
        return []

    if not API_KEY:
        logger.error("API_KEY not configured")
        return []

    try:
        response = httpx.get(
            url=ACVA_REPORT_FIELDS.format(task_id=task_id),
            headers={"X-API-Key": API_KEY},
        )

        domain_insights = []
        if response.status_code == 200:
            response_data = response.json()
            prompts = response_data.get("prompts", {}) if response_data else {}

            for key, value in prompts.items():
                domain_insight = DomainInsight(
                    insight_name=key, insight_extraction_prompt=value
                )
                domain_insights.append(domain_insight)

        return domain_insights

    except Exception as e:
        logger.error(f"Failed to retrieve domain insights for task {task_id}: {e}")
        return []


def test():
    """Test function for domain insights retrieval."""
    task_id = "702814b8-2068-4cc1-b8df-4b0ec09e6243"
    # TODO test with a valid task_id
    print(retrieve_domain_insights(task_id))


if __name__ == "__main__":
    test()
