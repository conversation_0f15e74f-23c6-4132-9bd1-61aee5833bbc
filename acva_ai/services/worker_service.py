#!/usr/bin/env python3
"""
RQ Worker Service

This module starts RQ workers to process batch jobs and report generation tasks.
"""

import logging
import os
import signal
import sys
from typing import List

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from acva_ai.services.queue_config import queue_client

logger = logging.getLogger(__name__)


class WorkerService:
    """Service to manage RQ workers for batch processing."""

    def __init__(self):
        self.workers: List = []
        self.running = False

    def start(self):
        """Start the RQ workers."""
        logger.info("Starting RQ Worker Service...")

        # Test Redis connection
        if not queue_client.test_connection():
            logger.error("Failed to connect to Redis. Exiting.")
            sys.exit(1)

        try:
            # Create worker for batch processing and report generation
            worker = queue_client.create_worker()
            self.workers.append(worker)

            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)

            logger.info(
                f"Starting worker {worker.name} for queues: {[q.name for q in worker.queues]}"
            )

            self.running = True
            worker.work(with_scheduler=True, logging_level="INFO")

        except Exception as e:
            logger.error(f"Error starting worker: {e}")
            sys.exit(1)
        finally:
            self.cleanup()

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down workers...")
        self.running = False

        for worker in self.workers:
            worker.request_stop()

    def cleanup(self):
        """Cleanup worker resources."""
        logger.info("Cleaning up worker resources...")


def main():
    """Main entry point for the worker service."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("/tmp/worker.log"),
        ],
    )

    logger.info("Starting ACVA AI Worker Service...")

    service = WorkerService()
    service.start()


if __name__ == "__main__":
    main()
