import io
import logging
from typing import List, Optional

from minio import Minio
from minio.error import S3Error

from acva_ai._params import (
    MINIO_PORT,
    MINIO_ROOT_PASSWORD,
    MINIO_ROOT_USER,
    MINIO_SERVER,
)

if not MINIO_SERVER:
    raise ValueError("MINIO_SERVER environment variable is not set.")
if not MINIO_PORT:
    raise ValueError("MINIO_PORT environment variable is not set.")

logger = logging.getLogger(__name__)


class MinioClient:
    def __init__(
        self,
        endpoint=f"{MINIO_SERVER}:{int(MINIO_PORT)}",
        access_key=MINIO_ROOT_USER,
        secret_key=MINIO_ROOT_PASSWORD,
        secure=False,
        bucket_name="acva-audio",
    ):
        self.client = Minio(
            endpoint, access_key=access_key, secret_key=secret_key, secure=secure
        )
        self.bucket_name = bucket_name
        self._ensure_bucket()

    def _ensure_bucket(self):
        if not self.client.bucket_exists(self.bucket_name):
            self.client.make_bucket(self.bucket_name)

    def upload_file(
        self, object_name, data, length, content_type="application/octet-stream"
    ):
        self.client.put_object(
            self.bucket_name, object_name, data, length, content_type=content_type
        )

    def download_file(self, object_name):
        response = self.client.get_object(self.bucket_name, object_name)
        return response.read()

    def list_files(self, prefix=""):
        return [
            obj.object_name
            for obj in self.client.list_objects(self.bucket_name, prefix=prefix)
        ]

    def delete_file(self, object_name):
        try:
            self.client.remove_object(self.bucket_name, object_name)
            logger.info(f"Deleted file: {object_name}")
        except S3Error as e:
            logger.error(f"Failed to delete file {object_name}: {e}")
            raise

    def delete_files(self, object_names: List[str]):
        """Delete multiple files efficiently."""
        try:
            from minio.deleteobjects import DeleteObject

            delete_objects = [DeleteObject(name) for name in object_names]
            errors = self.client.remove_objects(self.bucket_name, delete_objects)

            error_count = 0
            for error in errors:
                logger.error(f"Failed to delete file: {error}")
                error_count += 1

            if error_count == 0:
                logger.info(
                    f"Successfully deleted {len(object_names)} files from MinIO"
                )
            else:
                logger.warning(f"Deleted files with {error_count} errors")

        except Exception as e:
            logger.error(f"Failed to delete files: {e}")
            raise

    def cleanup_batch_files(self, batch_id: str):
        """Clean up batch result files after assembly is complete."""
        try:
            # Clean up batch_results files for this batch
            prefix = f"batch_results/{batch_id}/"
            files = self.list_files(prefix)

            files_to_delete = []
            for file_path in files:
                if file_path and isinstance(file_path, str):
                    files_to_delete.append(file_path)

            if files_to_delete:
                self.delete_files(files_to_delete)
                logger.info(
                    f"Cleaned up {len(files_to_delete)} batch result files for batch {batch_id}"
                )
            else:
                logger.debug(
                    f"No batch result files found to clean up for batch {batch_id}"
                )

        except Exception as e:
            logger.error(f"Failed to cleanup batch files for {batch_id}: {e}")

    def cleanup_raw_audio_files(self, visit_job_id: str, batch_number: int):
        """Clean up raw audio files after batch processing is complete."""
        try:
            prefix = f"raw_audio/{visit_job_id}/{batch_number}/"
            files = self.list_files(prefix)

            # Delete all files in this batch's directory
            files_to_delete = []
            for file_path in files:
                if file_path and isinstance(file_path, str):
                    files_to_delete.append(file_path)

            if files_to_delete:
                self.delete_files(files_to_delete)
                logger.info(
                    f"Cleaned up {len(files_to_delete)} raw audio files for batch {batch_number}"
                )

        except Exception as e:
            logger.error(
                f"Failed to cleanup raw audio files for batch {batch_number}: {e}"
            )

    def file_exists(self, object_name: str) -> bool:
        """Check if a file exists in MinIO."""
        try:
            self.client.stat_object(self.bucket_name, object_name)
            return True
        except S3Error:
            return False


# Example usage:
# minio_client = MinioClient()
# with open("test.wav", "rb") as f:
#     minio_client.upload_file("test.wav", f, length=os.path.getsize("test.wav"), content_type="audio/wav")
