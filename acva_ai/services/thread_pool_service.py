"""
Thread Pool Executor Service

Provides a centralized thread pool executor service for managing concurrent I/O operations
across the application, particularly for audio processing tasks.
"""

import asyncio
import functools
import logging
import os
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Callable, Optional

logger = logging.getLogger(__name__)


class ThreadPoolExecutorService:
    """
    Centralized thread pool executor service for managing concurrent I/O operations.

    This service provides a shared thread pool to prevent creating too many threads
    and to efficiently manage resources across different parts of the application.
    """

    _instance: Optional["ThreadPoolExecutorService"] = None
    _executor: Optional[ThreadPoolExecutor] = None

    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize the thread pool executor service.

        Args:
            max_workers: Maximum number of worker threads.
        """
        if max_workers is None:
            cpu_count = os.cpu_count() or 4
            max_workers = min(32, cpu_count + 4)

        self.max_workers = max_workers
        self._executor = None
        logger.info(
            f"ThreadPoolExecutorService initialized with max_workers={max_workers} (cpu_count={os.cpu_count()})"
        )

    @classmethod
    def get_instance(
        cls, max_workers: Optional[int] = None
    ) -> "ThreadPoolExecutorService":
        """
        Get the singleton instance of ThreadPoolExecutorService.

        Args:
            max_workers: Maximum number of worker threads (only used on first call).
                        If None, uses optimal default: min(32, cpu_count + 4)

        Returns:
            ThreadPoolExecutorService instance
        """
        if cls._instance is None:
            cls._instance = cls(max_workers=max_workers)
        return cls._instance

    @property
    def executor(self) -> ThreadPoolExecutor:
        """
        Get the thread pool executor, creating it if necessary.

        Returns:
            ThreadPoolExecutor instance
        """
        if self._executor is None or self._executor._shutdown:
            self._executor = ThreadPoolExecutor(
                max_workers=self.max_workers, thread_name_prefix="acva_ai_pool"
            )
            logger.debug(
                f"Created new ThreadPoolExecutor with {self.max_workers} workers"
            )
        return self._executor

    async def run_in_executor(self, func: Callable, *args, **kwargs) -> Any:
        """
        Run a function in the thread pool executor.

        Args:
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the function execution
        """
        loop = asyncio.get_event_loop()

        # If kwargs are provided, create a wrapper function
        if kwargs:

            def wrapper():
                return func(*args, **kwargs)

            return await loop.run_in_executor(self.executor, wrapper)
        else:
            return await loop.run_in_executor(self.executor, func, *args)

    async def gather_in_executor(self, tasks: list) -> list:
        """
        Execute multiple tasks concurrently in the thread pool.

        Args:
            tasks: List of tuples (func, args, kwargs) representing tasks to execute

        Returns:
            List of results from all tasks
        """
        coroutines = []

        for task in tasks:
            if len(task) == 2:
                func, args = task
                kwargs = {}
            elif len(task) == 3:
                func, args, kwargs = task
            else:
                raise ValueError(
                    "Each task must be a tuple of (func, args) or (func, args, kwargs)"
                )

            coroutines.append(self.run_in_executor(func, *args, **kwargs))

        return await asyncio.gather(*coroutines)

    async def gather_partial_functions(self, func: Callable, args_list: list) -> list:
        """
        Execute the same function with different arguments concurrently.

        This is the most efficient approach when calling the same function
        with different arguments.

        Args:
            func: The function to execute
            args_list: List of argument tuples for each function call

        Returns:
            List of results from all function calls

        Example:
            args_list = [(i, path) for i, path in enumerate(minio_paths)]
            results = await service.gather_partial_functions(download_and_process_file, args_list)
        """
        callables = [
            (
                functools.partial(func, *args)
                if isinstance(args, tuple)
                else functools.partial(func, args)
            )
            for args in args_list
        ]
        return await self.gather_callables(callables)

    async def gather_callables(self, callables: list) -> list:
        """
        Execute multiple callable objects concurrently in the thread pool.

        This is a cleaner, more Pythonic approach for Python 3.11+.

        Args:
            callables: List of callable objects (functions, lambdas, functools.partial, etc.)

        Returns:
            List of results from all callables

        Example:
            callables = [
                lambda: download_file("path1"),
                functools.partial(process_file, "path2", format="wav"),
                lambda: expensive_computation(data)
            ]
            results = await service.gather_callables(callables)
        """
        coroutines = [self.run_in_executor(callable_obj) for callable_obj in callables]
        return await asyncio.gather(*coroutines)

    async def gather_with_asyncio_to_thread(
        self, func: Callable, args_list: list
    ) -> list:
        """
        Execute the same function with different arguments using asyncio.to_thread.
        Uses the default thread pool managed by asyncio.

        Args:
            func: The function to execute
            args_list: List of argument tuples for each function call

        Returns:
            List of results from all function calls

        Example:
            args_list = [(i, path) for i, path in enumerate(minio_paths)]
            results = await service.gather_with_asyncio_to_thread(download_and_process_file, args_list)
        """
        coroutines = [
            (
                asyncio.to_thread(func, *args)
                if isinstance(args, tuple)
                else asyncio.to_thread(func, args)
            )
            for args in args_list
        ]
        return await asyncio.gather(*coroutines)

    def shutdown(self, wait: bool = True) -> None:
        """
        Shutdown the thread pool executor.

        Args:
            wait: Whether to wait for all threads to complete
        """
        if self._executor and not self._executor._shutdown:
            logger.info("Shutting down ThreadPoolExecutor")
            self._executor.shutdown(wait=wait)

    def __del__(self):
        """Cleanup on garbage collection."""
        if (
            hasattr(self, "_executor")
            and self._executor
            and not self._executor._shutdown
        ):
            self._executor.shutdown(wait=False)


# Global instance will be created lazily when first accessed
# Uses optimal default: min(32, cpu_count + 4) threads
# On typical 8-core systems: 12 threads
# On high-core systems: capped at 32 threads
thread_pool_service = None


def get_thread_pool_service(
    max_workers: Optional[int] = None,
) -> ThreadPoolExecutorService:
    """
    Get the global thread pool service instance.

    Args:
        max_workers: Maximum number of worker threads (only used on first call).
                    If None, uses optimal default: min(32, cpu_count + 4)

    Returns:
        ThreadPoolExecutorService instance
    """
    global thread_pool_service
    if thread_pool_service is None:
        thread_pool_service = ThreadPoolExecutorService.get_instance(
            max_workers=max_workers
        )
    return thread_pool_service


def get_high_concurrency_thread_pool_service() -> ThreadPoolExecutorService:
    """
    Get a thread pool service optimized for high-concurrency I/O operations.

    Uses a higher thread count suitable for scenarios with many concurrent
    I/O-bound tasks (like processing 25 files simultaneously).

    Note: This creates a separate instance, not the singleton.

    Returns:
        ThreadPoolExecutorService instance with max_workers = min(50, cpu_count * 6)
    """
    cpu_count = os.cpu_count() or 4
    max_workers = min(50, cpu_count * 6)  # Higher for I/O-bound tasks
    return ThreadPoolExecutorService(max_workers=max_workers)
