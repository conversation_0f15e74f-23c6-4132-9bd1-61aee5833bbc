import asyncio
import logging
from functools import lru_cache
from typing import Any, Dict, List, Optional

import aiohttp
from openai import APIConnectionError, APIError, AzureOpenAI, RateLimitError
from qdrant_client import QdrantClient
from qdrant_client.http import models as rest

from acva_ai._params import (
    AZURE_API_VERSION,
    AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_ENDPOINT,
    QDRANT_SERVER,
)

logger = logging.getLogger(__name__)


class QdrantService:
    """Service for interacting with Qdrant vector database."""

    def __init__(
        self,
        location: str = QDRANT_SERVER,
        port: int = 6333,
        api_key: Optional[str] = None,
        timeout: int = 60,
    ):
        """
        Initialize the Qdrant service.

        Args:
            location: Qdrant server location
            port: Qdrant server port
            api_key: Optional API key for cloud deployments
            timeout: Connection timeout in seconds
        """
        self.location = location
        self.port = port
        self.api_key = api_key
        self.timeout = timeout
        self._client = None

    @property
    def client(self) -> QdrantClient:
        """
        Lazy-loaded Qdrant client with connection pooling.

        Returns:
            QdrantClient: Configured Qdrant client
        """
        if self._client is None:
            if self.location.startswith(("http://", "https://")):
                # Cloud deployment
                self._client = QdrantClient(
                    url=self.location, api_key=self.api_key, timeout=self.timeout
                )
            else:
                # Local deployment
                self._client = QdrantClient(
                    location=self.location, port=self.port, timeout=self.timeout
                )
            logger.info(f"Initialized Qdrant client: {self.location}")
        return self._client

    def create_collection(
        self,
        collection_name: str,
        vector_size: int,
        distance: str = "Cosine",
        on_disk: bool = False,
        optimizers_config: Optional[Dict[str, Any]] = None,
        shard_number: Optional[int] = None,
    ) -> bool:
        """
        Create a new collection in Qdrant.

        Args:
            collection_name: Name of the collection
            vector_size: Dimensionality of vectors
            distance: Distance metric (Cosine, Euclid, Dot)
            on_disk: Whether to store vectors on disk
            optimizers_config: Optional optimizer configuration
            shard_number: Optional number of shards

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if collection already exists
            collections = self.client.get_collections()
            collection_names = [
                collection.name for collection in collections.collections
            ]

            if collection_name in collection_names:
                logger.info(f"Collection '{collection_name}' already exists")
                return True

            # Create vector configuration
            vector_config = rest.VectorParams(
                size=vector_size, distance=rest.Distance(distance), on_disk=on_disk
            )

            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=vector_config,
                optimizers_config=optimizers_config,
                shard_number=shard_number,
            )

            logger.info(f"Successfully created collection '{collection_name}'")
            return True
        except Exception as e:
            logger.error(f"Error creating collection '{collection_name}': {str(e)}")
            return False

    def upsert_points(
        self,
        collection_name: str,
        points: List[rest.PointStruct],
        batch_size: int = 100,
    ) -> bool:
        """
        Insert or update points in a collection with batching.

        Args:
            collection_name: Name of the collection
            points: List of points to insert/update
            batch_size: Size of batches for insertion

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Process in batches to avoid overwhelming the server
            for i in range(0, len(points), batch_size):
                batch = points[i : i + batch_size]
                self.client.upsert(collection_name=collection_name, points=batch)
                logger.debug(
                    f"Inserted batch {i//batch_size + 1}/{(len(points) + batch_size - 1)//batch_size}"
                )

            logger.info(
                f"Successfully upserted {len(points)} points to '{collection_name}'"
            )
            return True
        except Exception as e:
            logger.error(f"Error upserting points to '{collection_name}': {str(e)}")
            return False

    def search(
        self,
        collection_name: str,
        query: str,
        limit: int = 10,
        filter_condition: Optional[Dict[str, Any]] = None,
        with_payload: bool = True,
        with_vectors: bool = False,
        score_threshold: Optional[float] = None,
    ) -> List[rest.ScoredPoint]:
        """
        Search for similar vectors in a collection.

        Args:
            collection_name: Name of the collection
            query: Query vector or text
            limit: Maximum number of results
            filter_condition: Optional filter condition
            with_payload: Whether to include payload in results
            with_vectors: Whether to include vectors in results
            score_threshold: Minimum similarity score threshold

        Returns:
            List[ScoredPoint]: List of search results
        """
        try:
            results = self.client.query_points(
                collection_name=collection_name,
                query=query,
                limit=limit,
                query_filter=filter_condition,
                with_payload=with_payload,
                with_vectors=with_vectors,
                score_threshold=score_threshold,
            )
            return results
        except Exception as e:
            logger.error(f"Error searching in collection '{collection_name}': {str(e)}")
            return []

    def clear_collection(self, collection_name: str) -> bool:
        """
        Clear all points from a collection.

        Args:
            collection_name: Name of the collection

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [
                collection.name for collection in collections.collections
            ]

            if collection_name not in collection_names:
                logger.warning(f"Collection '{collection_name}' does not exist")
                return False

            # Delete all points from the collection
            self.client.delete(
                collection_name=collection_name,
                points_selector=None,  # None means delete all points
            )
            logger.info(
                f"Successfully cleared all points from collection '{collection_name}'"
            )
            return True
        except Exception as e:
            logger.error(f"Error clearing collection '{collection_name}': {str(e)}")
            return False

    def delete_collection(self, collection_name: str) -> bool:
        """
        Delete a collection entirely.

        Args:
            collection_name: Name of the collection

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [
                collection.name for collection in collections.collections
            ]

            if collection_name not in collection_names:
                logger.warning(f"Collection '{collection_name}' does not exist")
                return False

            # Delete the collection
            self.client.delete_collection(collection_name=collection_name)
            logger.info(f"Successfully deleted collection '{collection_name}'")
            return True
        except Exception as e:
            logger.error(f"Error deleting collection '{collection_name}': {str(e)}")
            return False

    def reset_client(self) -> bool:
        """
        Reset the client by deleting all collections.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get all collections
            collections = self.client.get_collections()
            collection_names = [
                collection.name for collection in collections.collections
            ]

            # Delete each collection
            for name in collection_names:
                self.client.delete_collection(collection_name=name)
                logger.info(f"Deleted collection '{name}'")

            logger.info(
                f"Successfully reset Qdrant client, removed {len(collection_names)} collections"
            )
            return True
        except Exception as e:
            logger.error(f"Error resetting Qdrant client: {str(e)}")
            return False


class EmbeddingService:
    """Service for generating embeddings using Azure OpenAI."""

    def __init__(
        self,
        api_key: str = AZURE_OPENAI_API_KEY,
        endpoint: str = AZURE_OPENAI_ENDPOINT,
        api_version: str = AZURE_API_VERSION,
        model: str = "text-embedding-3-small",
        cache_size: int = 1000,
    ):
        """
        Initialize the embedding service.

        Args:
            api_key: Azure OpenAI API key
            endpoint: Azure OpenAI endpoint
            api_version: Azure API version
            model: Embedding model to use
            cache_size: Size of the LRU cache
        """
        self.api_key = api_key
        self.endpoint = endpoint
        self.api_version = api_version
        self.model = model

        # Initialize Azure OpenAI client
        self.client = AzureOpenAI(
            api_key=api_key,
            api_version=api_version,
            azure_endpoint=endpoint,
        )

        # Create a sync wrapper for caching
        self._embed_text_cached = lru_cache(maxsize=cache_size)(
            self._embed_text_sync_wrapper
        )

    def _embed_text_sync_wrapper(self, text: str) -> List[float]:
        """
        Synchronous wrapper for caching embeddings.

        Args:
            text: Text to embed

        Returns:
            List[float]: Embedding vector
        """
        # This will be called by the cached version
        # We need to run the async function in the current event loop
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, we need to create a task
                # But since lru_cache expects a sync function, we'll use a different approach
                # Store the result in a thread-safe way
                import concurrent.futures

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        asyncio.run, self._embed_text_uncached(text)
                    )
                    return future.result()
            else:
                return asyncio.run(self._embed_text_uncached(text))
        except RuntimeError:
            # Fallback for when no event loop is available
            return asyncio.run(self._embed_text_uncached(text))

    async def _embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for a single text with caching.

        Args:
            text: Text to embed

        Returns:
            List[float]: Embedding vector
        """
        # For async calls, we'll bypass the cache for now to avoid coroutine reuse issues
        # In production, consider using an async-compatible cache like aiocache
        return await self._embed_text_uncached(text)

    async def _embed_text_uncached(self, text: str) -> List[float]:
        """
        Generate embedding for a single text (uncached version).

        Args:
            text: Text to embed

        Returns:
            List[float]: Embedding vector
        """
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                # Use Azure OpenAI client to create embeddings
                response = self.client.embeddings.create(model=self.model, input=text)

                # Extract the embedding from the response
                return response.data[0].embedding

            except RateLimitError as e:
                if attempt < max_retries:
                    wait_time = retry_delay * (2**attempt)
                    logger.warning(
                        f"Rate limit reached, waiting {wait_time}s before retry"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(
                        f"Rate limit exceeded after {max_retries} attempts: {str(e)}"
                    )
                    raise

            except (APIError, APIConnectionError) as e:
                if attempt < max_retries:
                    wait_time = retry_delay * (2**attempt)
                    logger.warning(
                        f"Embedding attempt {attempt+1} failed: {str(e)}. Retrying in {wait_time}s"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"All embedding attempts failed: {str(e)}")
                    raise

            except Exception as e:
                logger.error(f"Unexpected error during embedding: {str(e)}")
                raise

    async def embed_texts(
        self, texts: List[str], batch_size: int = 20
    ) -> List[List[float]]:
        """
        Generate embeddings for multiple texts with batching.

        Args:
            texts: List of texts to embed
            batch_size: Size of batches for API calls

        Returns:
            List[List[float]]: List of embedding vectors
        """
        results = []

        # Process in batches to respect API limits
        for i in range(0, len(texts), batch_size):
            batch = texts[i : i + batch_size]
            batch_tasks = [self._embed_text(text) for text in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Handle any exceptions
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Error embedding text at index {i+j}: {str(result)}")
                    # Use a zero vector as fallback
                    results.append([0.0] * 1536)  # Assuming 1536-dimensional embeddings
                else:
                    results.append(result)

            # Add a small delay between batches to avoid rate limits
            if i + batch_size < len(texts):
                await asyncio.sleep(0.5)

        return results


class RAGService:
    """Service for Retrieval-Augmented Generation."""

    def __init__(
        self,
        qdrant_service: Optional[QdrantService] = None,
        embedding_service: Optional[EmbeddingService] = None,
    ):
        """
        Initialize the RAG service.

        Args:
            qdrant_service: Optional QdrantService instance
            embedding_service: Optional EmbeddingService instance
        """
        self.qdrant = qdrant_service or QdrantService()
        self.embeddings = embedding_service or EmbeddingService()

    async def query(
        self,
        collection_name: str,
        query_text: str,
        limit: int = 5,
        filter_condition: Optional[Dict[str, Any]] = None,
        score_threshold: Optional[float] = 0.7,
    ) -> List[Dict[str, Any]]:
        """
        Query the RAG system with natural language.

        Args:
            collection_name: Name of the collection to query
            query_text: Natural language query
            limit: Maximum number of results
            filter_condition: Optional filter condition
            score_threshold: Minimum similarity score

        Returns:
            List[Dict[str, Any]]: List of relevant documents with metadata
        """
        try:
            # Generate embedding for the query
            query_embedding = await self.embeddings._embed_text(query_text)

            # Search for similar documents
            results = self.qdrant.search(
                collection_name=collection_name,
                query=query_embedding,
                limit=limit,
                filter_condition=filter_condition,
                score_threshold=score_threshold,
            )

            # Format results - handle both list of ScoredPoint and tuple returns
            formatted_results = []

            # Debug the results type
            logger.debug(f"Results type: {type(results)}")

            # Check if results is a list of ScoredPoint objects
            if isinstance(results, list):
                for result in results:
                    if (
                        hasattr(result, "score")
                        and hasattr(result, "payload")
                        and hasattr(result, "id")
                    ):
                        formatted_results.append(
                            {
                                "score": result.score,
                                "payload": result.payload,
                                "id": result.id,
                            }
                        )
            # If results is a tuple with points attribute (Qdrant response object)
            elif hasattr(results, "points"):
                for point in results.points:
                    formatted_results.append(
                        {"score": point.score, "payload": point.payload, "id": point.id}
                    )

            return formatted_results
        except Exception as e:
            logger.error(f"Error querying RAG system: {str(e)}")
            return []

    async def index_documents(
        self,
        collection_name: str,
        documents: List[Dict[str, Any]],
        text_field: str,
        metadata_fields: Optional[List[str]] = None,
        vector_size: int = 1536,
        batch_size: int = 100,
    ) -> bool:
        """
        Index documents in the RAG system.

        Args:
            collection_name: Name of the collection
            documents: List of documents to index
            text_field: Field containing the text to embed
            metadata_fields: Fields to include as metadata
            vector_size: Dimensionality of vectors
            batch_size: Size of batches for audio_processing

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure collection exists
            self.qdrant.create_collection(
                collection_name=collection_name, vector_size=vector_size
            )

            # Extract texts and metadata
            texts = []
            metadata_list = []

            for doc in documents:
                if text_field not in doc:
                    logger.warning(
                        f"Document missing text field '{text_field}', skipping"
                    )
                    continue

                texts.append(doc[text_field])

                # Extract metadata
                metadata = {}
                if metadata_fields:
                    for field in metadata_fields:
                        if field in doc:
                            metadata[field] = doc[field]
                metadata_list.append(metadata)

            # Generate embeddings
            embeddings = await self.embeddings.embed_texts(texts)

            # Create points
            points = []
            for i, (embedding, metadata) in enumerate(zip(embeddings, metadata_list)):
                # Add the text to the metadata
                metadata[text_field] = texts[i]

                points.append(
                    rest.PointStruct(
                        id=str(i),  # Use a more robust ID in production
                        vector=embedding,
                        payload=metadata,
                    )
                )

            # Insert points
            success = self.qdrant.upsert_points(
                collection_name=collection_name, points=points, batch_size=batch_size
            )

            return success
        except Exception as e:
            logger.error(f"Error indexing documents: {str(e)}")
            return False


qdrant_service = QdrantService()
embedding_service = EmbeddingService()
rag_service = RAGService(qdrant_service, embedding_service)


def test():
    qdrant_service = QdrantService(location="localhost")
    embedding_service = EmbeddingService()
    rag_service = RAGService(qdrant_service, embedding_service)
    print(
        asyncio.run(rag_service.query("medical_terms_dictionary", query_text="abdomen"))
    )


if __name__ == "__main__":
    test()
