from typing import List, Optional

from pydantic import BaseModel


class LLMUsage(BaseModel):
    desc: Optional[str] = None
    model_id: Optional[str] = None
    cost: Optional[float] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    audio_input_duration: Optional[float] = None


class ResponseUsage(BaseModel):
    cost: Optional[float] = 0
    llm_usage: Optional[List[LLMUsage]] = []

    def __add__(self, other):
        if not isinstance(other, ResponseUsage):
            return NotImplemented

        # Combine llm_usage lists
        combined_llm_usage = (self.llm_usage or []) + (other.llm_usage or [])

        # Return a new ResponseUsage object with combined values
        return ResponseUsage(
            cost=(self.cost or 0) + (other.cost or 0), llm_usage=combined_llm_usage
        )

    def add_llm_usage(self, llm: LLMUsage):
        """Adds an LLMUsage element and updates the cost and time."""
        if not self.llm_usage:
            self.llm_usage = []
        self.llm_usage.append(llm)

        if self.cost:
            self.cost += llm.cost or 0
