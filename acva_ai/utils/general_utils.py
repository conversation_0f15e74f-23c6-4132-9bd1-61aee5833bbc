import os
import logging
from fastapi import Header, HTTPException
from collections.abc import Iterable
from acva_ai._params import API_KEY

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def flatten_list(nested_list):
    """Recursively flatten a nested list."""
    for item in nested_list:
        if isinstance(item, Iterable) and not isinstance(item, (str, bytes)):
            yield from flatten_list(item)
        else:
            yield item


async def cleanup_files(file_paths):
    """Remove files from a possibly nested list of file paths."""
    flat_paths = list(flatten_list(file_paths))

    for path in flat_paths:
        if os.path.exists(path):
            try:
                os.remove(path)
                logger.info(f"Removed file: {path}")
            except Exception as e:
                logger.error(f"Error removing file {path}: {e}")


def verify_api_key(api_key: str = Header(None, alias="X-API-Key")):
    """
    Verifies that the provided API key is valid.

    Args:
        api_key: The API key provided in the request header.

    Returns:
        The API key if valid.

    Raises:
        HTTPException: If the API key is missing or invalid.
    """
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail="API Key is required",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    if api_key != API_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid API Key",
            headers={"WWW-Authenticate": "ApiKey"},
        )

    return api_key


async def calculate_cost(
    model: str,
    input_tokens: int = 0,
    output_tokens: int = 0,
    audio_duration_seconds: float = 0,
) -> float:
    pricing = {
        "gpt-4o": {
            "input_per_1k": 0.0025,
            "output_per_1k": 0.0050,
        },
        "gpt-4o-mini": {
            "input_per_1k": 0.0003,
            "output_per_1k": 0.0004,
        },
        "gpt-4o-transcribe": {
            "transcribe_per_minute": 0.006,
        },
        "scribe_v1": {"transcribe_per_minute": 0.0058},
    }

    if model not in pricing:
        raise ValueError(
            f"Unsupported model: {model}. Supported models are: {', '.join(pricing.keys())}"
        )

    if model == "gpt-4o-transcribe" or model == "scribe_v1":
        gpt_4_transcribe_cost = (audio_duration_seconds / 60) * pricing[model][
            "transcribe_per_minute"
        ]
        return gpt_4_transcribe_cost

    model_pricing = pricing[model]
    input_cost = (input_tokens / 1000) * model_pricing["input_per_1k"]
    output_cost = (output_tokens / 1000) * model_pricing["output_per_1k"]

    total_cost = input_cost + output_cost

    return total_cost
