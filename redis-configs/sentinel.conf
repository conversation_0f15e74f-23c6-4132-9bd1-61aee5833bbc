# Redis Sentinel Configuration
port 26379
bind 0.0.0.0

# Sentinel settings
sentinel monitor acva-redis redis-master 6379 2
sentinel down-after-milliseconds acva-redis 30000
sentinel parallel-syncs acva-redis 1
sentinel failover-timeout acva-redis 180000

# Sentinel auth (if Redis instances have auth)
# sentinel auth-pass acva-redis your_redis_password

# Logging
logfile ""
loglevel notice

# Security
protected-mode no

# Announce settings for Docker networking
# The announce-ip will be set dynamically via environment variable
sentinel announce-port 26379

# Notification scripts (optional)
# sentinel notification-script acva-redis /path/to/notify.sh
# sentinel client-reconfig-script acva-redis /path/to/reconfig.sh

# Deny dangerous commands
sentinel deny-scripts-reconfig yes