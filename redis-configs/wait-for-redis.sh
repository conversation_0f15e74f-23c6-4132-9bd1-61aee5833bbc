#!/bin/sh

# Wait for Redis master to be available
# Usage: ./wait-for-redis.sh redis-master 6379 sentinel-config.conf

REDIS_HOST=${1:-redis-master}
REDIS_PORT=${2:-6379}
SENTINEL_CONFIG=${3:-/usr/local/etc/redis/sentinel.conf}

echo "Waiting for Redis master at $REDIS_HOST:$REDIS_PORT to be available..."

# Wait up to 60 seconds for Redis to be available
i=1
while [ $i -le 60 ]; do
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1; then
        echo "Redis master is available!"
        break
    fi
    
    if [ $i -eq 60 ]; then
        echo "Timeout waiting for Redis master at $REDIS_HOST:$REDIS_PORT"
        exit 1
    fi
    
    echo "Waiting for Redis master... ($i/60)"
    sleep 1
    i=$((i + 1))
done

# Function to resolve hostname to IP
resolve_hostname() {
    local hostname=$1
    local ip=""
    
    # Try nslookup first
    ip=$(nslookup "$hostname" 2>/dev/null | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
    
    if [ -z "$ip" ]; then
        # Try getent if available
        if command -v getent > /dev/null 2>&1; then
            ip=$(getent hosts "$hostname" 2>/dev/null | awk '{print $1}' | head -1)
        fi
    fi
    
    if [ -z "$ip" ]; then
        # Try ping as last resort
        ip=$(ping -c 1 "$hostname" 2>/dev/null | grep "PING" | sed -n 's/.*(\([^)]*\)).*/\1/p')
    fi
    
    echo "$ip"
}

# Resolve all relevant hostnames
echo "Resolving IP addresses for all Redis components..."

REDIS_MASTER_IP=$(resolve_hostname "$REDIS_HOST")
REDIS_REPLICA_IP=$(resolve_hostname "redis-replica")

echo "Resolved IPs:"
echo "  redis-master -> $REDIS_MASTER_IP"
echo "  redis-replica -> $REDIS_REPLICA_IP"

if [ -n "$REDIS_MASTER_IP" ]; then
    # Create a temporary sentinel config with IP addresses
    TEMP_CONFIG="/tmp/sentinel.conf"
    cp "$SENTINEL_CONFIG" "$TEMP_CONFIG"
    
    # Replace master hostname with IP address (with correct quorum of 2)
    sed -i "s/sentinel monitor acva-redis $REDIS_HOST $REDIS_PORT 2/sentinel monitor acva-redis $REDIS_MASTER_IP $REDIS_PORT 2/" "$TEMP_CONFIG"
    
    echo "Updated Sentinel config to use IP addresses"
    SENTINEL_CONFIG="$TEMP_CONFIG"
else
    echo "Warning: Could not resolve IP for $REDIS_HOST, using hostname"
fi

# Wait a bit more to ensure everything is stable
echo "Waiting 2 more seconds for stability..."
sleep 2

echo "Starting Redis Sentinel with config: $SENTINEL_CONFIG"
exec redis-sentinel "$SENTINEL_CONFIG"