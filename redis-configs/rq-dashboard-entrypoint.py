#!/usr/bin/env python3

import os
import sys
import time


def discover_redis_master():
    """Discover Redis master via Sentinel"""

    try:
        from redis.sentinel import Sentinel
    except ImportError as e:
        print(f"❌ Failed to import Sentinel: {e}")
        return None

    # Sentinel configuration from environment variables
    sentinel_hosts_str = os.getenv(
        "SENTINEL_HOSTS",
        "acva-ai-sentinel-1:26379,acva-ai-sentinel-2:26379,acva-ai-sentinel-3:26379",
    )
    service_name = os.getenv("SENTINEL_SERVICE_NAME", "acva-redis")

    print(f"🔍 Discovering Redis master via Sentinel...")
    print(f"📡 Sentinel hosts: {sentinel_hosts_str}")
    print(f"🏷️  Service name: {service_name}")

    # Parse sentinel hosts from comma-separated string
    sentinels = []
    for host_port in sentinel_hosts_str.split(","):
        host_port = host_port.strip()
        if ":" in host_port:
            host, port = host_port.split(":", 1)
            sentinels.append((host.strip(), int(port.strip())))
        else:
            # Default port if not specified
            sentinels.append((host_port.strip(), 26379))

    print(f"📡 Parsed sentinel hosts: {sentinels}")

    try:
        # Create Sentinel connection
        print(f"🔗 Connecting to Sentinel cluster...")
        sentinel = Sentinel(sentinels, socket_timeout=5.0)

        # Discover master
        print(f"🔍 Discovering master for service '{service_name}'...")
        master_address = sentinel.discover_master(service_name)

        if master_address:
            master_ip, master_port = master_address
            redis_url = f"redis://{master_ip}:{master_port}/0"
            print(f"✅ Discovered Redis master: {redis_url}")
            return redis_url
        else:
            print("❌ Could not discover Redis master from Sentinel")
            return None

    except Exception as e:
        print(f"❌ Error connecting to Sentinel: {e}")
        print(f"🔧 Exception type: {type(e).__name__}")
        import traceback

        traceback.print_exc()
        return None


def main():
    """Main entrypoint function"""

    print("🚀 RQ Dashboard Sentinel-aware startup")
    print("=" * 50)

    # Show environment variables
    print("📋 Environment variables:")
    for key in [
        "SENTINEL_HOST",
        "BACKUP_SENTINEL_HOST",
        "SENTINEL_PORT",
        "SENTINEL_SERVICE_NAME",
    ]:
        value = os.getenv(key, "NOT SET")
        print(f"   {key}: {value}")
    print("=" * 50)

    # Try to discover Redis master
    max_retries = 10
    retry_delay = 3

    for attempt in range(1, max_retries + 1):
        print(f"🔄 Attempt {attempt}/{max_retries}")

        redis_url = discover_redis_master()

        if redis_url:
            # Set environment variable for RQ Dashboard
            os.environ["RQ_DASHBOARD_REDIS_URL"] = redis_url
            print(f"🎯 Set RQ_DASHBOARD_REDIS_URL: {redis_url}")
            print("🚀 Starting RQ Dashboard...")
            print("=" * 50)

            # Start RQ Dashboard
            try:
                # Use exec to replace the process completely
                os.execvp(sys.executable, [sys.executable, "-m", "rq_dashboard"])
            except Exception as e:
                print(f"❌ Failed to start RQ Dashboard: {e}")
                sys.exit(1)

            return

        if attempt < max_retries:
            print(f"⏳ Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
        else:
            print("❌ All retry attempts exhausted")

    print("💥 Failed to discover Redis master after all retries")
    print("🔄 Starting RQ Dashboard with default configuration as fallback...")

    # Fallback: start RQ Dashboard without Redis URL (will fail, but at least we'll see the error)
    try:
        os.execvp(sys.executable, [sys.executable, "-m", "rq_dashboard"])
    except Exception as e:
        print(f"❌ Failed to start RQ Dashboard even as fallback: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("🛑 RQ Dashboard startup interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"💥 Unexpected error in entrypoint: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
