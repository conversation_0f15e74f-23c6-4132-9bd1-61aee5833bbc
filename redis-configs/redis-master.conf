# Redis Master Configuration
port 6379
bind 0.0.0.0

# Persistence settings (RDB + AOF)
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# AOF (Append Only File) persistence
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru

# Replication settings
replica-read-only yes
replica-serve-stale-data yes

# Security
protected-mode no

# Logging
loglevel notice
logfile ""

# Client settings
timeout 0
tcp-keepalive 300

# Sentinel integration
replica-announce-ip redis-master
replica-announce-port 6379