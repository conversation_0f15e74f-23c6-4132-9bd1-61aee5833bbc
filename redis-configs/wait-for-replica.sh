#!/bin/sh

# Wait for Redis replica startup
# Usage: ./wait-for-replica.sh redis-master 6379 replica-config.conf

REDIS_MASTER_HOST=${1:-redis-master}
REDIS_MASTER_PORT=${2:-6379}
REPLICA_CONFIG=${3:-/usr/local/etc/redis/redis.conf}

echo "Waiting for Redis master at $REDIS_MASTER_HOST:$REDIS_MASTER_PORT to be available..."

# Wait up to 60 seconds for Redis master to be available
i=1
while [ $i -le 60 ]; do
    if redis-cli -h "$REDIS_MASTER_HOST" -p "$REDIS_MASTER_PORT" ping > /dev/null 2>&1; then
        echo "Redis master is available!"
        break
    fi
    
    if [ $i -eq 60 ]; then
        echo "Timeout waiting for Redis master at $REDIS_MASTER_HOST:$REDIS_MASTER_PORT"
        exit 1
    fi
    
    echo "Waiting for Redis master... ($i/60)"
    sleep 1
    i=$((i + 1))
done

# Function to resolve hostname to IP
resolve_hostname() {
    local hostname=$1
    local ip=""
    
    # Try nslookup first
    ip=$(nslookup "$hostname" 2>/dev/null | grep -A1 "Name:" | grep "Address:" | awk '{print $2}' | head -1)
    
    if [ -z "$ip" ]; then
        # Try getent if available
        if command -v getent > /dev/null 2>&1; then
            ip=$(getent hosts "$hostname" 2>/dev/null | awk '{print $1}' | head -1)
        fi
    fi
    
    if [ -z "$ip" ]; then
        # Try ping as last resort
        ip=$(ping -c 1 "$hostname" 2>/dev/null | grep "PING" | sed -n 's/.*(\([^)]*\)).*/\1/p')
    fi
    
    echo "$ip"
}

# Resolve IP addresses
echo "Resolving IP addresses..."

REDIS_MASTER_IP=$(resolve_hostname "$REDIS_MASTER_HOST")
REDIS_REPLICA_IP=$(resolve_hostname "redis-replica")

echo "Resolved IPs:"
echo "  redis-master -> $REDIS_MASTER_IP"
echo "  redis-replica -> $REDIS_REPLICA_IP"

if [ -n "$REDIS_MASTER_IP" ] && [ -n "$REDIS_REPLICA_IP" ]; then
    # Create a temporary replica config with IP addresses
    TEMP_CONFIG="/tmp/redis.conf"
    cp "$REPLICA_CONFIG" "$TEMP_CONFIG"
    
    # Replace master hostname with IP address
    sed -i "s/replicaof $REDIS_MASTER_HOST $REDIS_MASTER_PORT/replicaof $REDIS_MASTER_IP $REDIS_MASTER_PORT/" "$TEMP_CONFIG"
    
    # Replace replica announce IP with actual IP
    sed -i "s/replica-announce-ip redis-replica/replica-announce-ip $REDIS_REPLICA_IP/" "$TEMP_CONFIG"
    
    echo "Updated Redis replica config to use IP addresses"
    REPLICA_CONFIG="$TEMP_CONFIG"
else
    echo "Warning: Could not resolve all IPs, using hostnames"
fi

# Wait a bit more to ensure everything is stable
echo "Waiting 2 more seconds for stability..."
sleep 2

echo "Starting Redis replica with config: $REPLICA_CONFIG"
exec redis-server "$REPLICA_CONFIG"